const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_NODE,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_USER,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_PASS,
  <PERSON>LA<PERSON><PERSON><PERSON>ARCH_SYCN_USER,
  ELASTICSEARCH_SYNC_PASS,
} = process.env;
export const searchConfig = {
  node: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_NODE || 'http://localhost:9200',
  username: <PERSON><PERSON>ST<PERSON><PERSON><PERSON>CH_USER || '',
  password: <PERSON>LA<PERSON><PERSON><PERSON>ARCH_PASS || '',
  syncUser: <PERSON>LASTIC<PERSON>ARCH_SYCN_USER || '',
  syncPass: <PERSON>LA<PERSON><PERSON><PERSON>AR<PERSON>_SYNC_PASS || '',
};


