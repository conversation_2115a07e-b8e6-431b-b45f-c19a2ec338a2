const {
  STRIPE_PUBLISHABLE_KEY,
  STRIPE_SECRET_KEY,
  STRIPE_WH_SECRET_KEY,
  STRIPE_WH_ENDPOINT,
  STRIPE_SUCCESS_URL,
  STRIPE_CANCEL_URL,
  STRIPE_CURRENCY,
  APPLE_PAY_PVT_KEY,
  APPLE_PAY_ISSUER_ID,
  APPLE_PAY_KEY_ID,
  APPLE_PAY_VERIFY_URL,
  APPLE_CLIENT_ID,
  APPLE_PAY_ENV,
  SSLCOMMERZ_STORE_ID,
  SSLCOMMERZ_STORE_PASSWORD,
  SSLCOMMERZ_URL,
  SSLCOMMERZ_VALIDATION_URL,
  SSLCOMMERZ_SUCCESS_URL,
  SSLCOMMERZ_CANCEL_URL,
  SSLCOMMERZ_FAIL_URL,
  SSLCOMMERZ_CURRENCY,
  SSLCOMMERZ_IPN_URL,
} = process.env;

export const paymentConfig = {
  stripePubKey: STRIPE_PUBLISHABLE_KEY || '',
  stripeSecretKey: STRIPE_SECRET_KEY || '',
  stripeWHSecretKey: STRIPE_WH_SECRET_KEY || '',
  stripeWHEndpoint: STRIPE_WH_ENDPOINT || '',
  stripeSuccessUrl: STRIPE_SUCCESS_URL || '',
  stripeCancelUrl: STRIPE_CANCEL_URL || '',
  stripeCurrency: STRIPE_CURRENCY || 'usd',

  applePayPvtKey: APPLE_PAY_PVT_KEY || '',
  applePayIssuerId: APPLE_PAY_ISSUER_ID || '',
  applePayKeyId: APPLE_PAY_KEY_ID || '',
  applePayVerifyUrl: APPLE_PAY_VERIFY_URL || '',
  appleBundleId: APPLE_CLIENT_ID || '',
  applePayEnv: APPLE_PAY_ENV || '',

  sslcommerzStoreId: SSLCOMMERZ_STORE_ID || '',
  sslcommerzStorePassword: SSLCOMMERZ_STORE_PASSWORD || '',
  sslcommerzUrl: SSLCOMMERZ_URL || '',
  sslcommerzValidationUrl: SSLCOMMERZ_VALIDATION_URL || '',
  sslcommerzSuccessUrl: SSLCOMMERZ_SUCCESS_URL || '',
  sslcommerzCancelUrl: SSLCOMMERZ_CANCEL_URL || '',
  sslcommerzFailUrl: SSLCOMMERZ_FAIL_URL || '',
  sslcommerzCurrency: SSLCOMMERZ_CURRENCY || 'BDT',
  sslcommerzIpnUrl: SSLCOMMERZ_IPN_URL || '',
};
