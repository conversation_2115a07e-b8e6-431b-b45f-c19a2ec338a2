const {
  NODEMAILER_HOST,
  NO<PERSON><PERSON><PERSON><PERSON>_USER,
  NO<PERSON><PERSON><PERSON><PERSON>_PASS,
  NO<PERSON><PERSON><PERSON>ER_PORT,
  NODEMA<PERSON>ER_SERVICE,
  NODEMAILER_SECURE,
  EMAIL_TRANSPORT_TYPE,
  SENDGRID_API_KEY,
  TWILIO_SID,
  TWILIO_AUTH_TOKEN,
  TWILIO_NUMBER,
  ACS_CONNECTION_STRING,
} = process.env;

export const nodemailerConfig = {
  user: NODEMAILER_USER,
  options: {
    host: NODEMAILER_HOST || 'smtp.example.com',
    port: Number(NODEMAILER_PORT) || 465,
    secure: Boolean(NODEMAILER_SECURE) || true,
    service: NODEMAILER_SERVICE || 'gmail',
    auth: {
      user: NODEMAILER_USER,
      pass: NODEMAILER_PASS,
    },
  },
};

export const mailerConfig = {
  emailTransportType: EMAIL_TRANSPORT_TYPE || 'NODEMAILER',
  sendgridApiKey: SENDGRID_API_KEY || '',
  acsConnectionString: ACS_CONNECTION_STRING || '',
};

export const twilioConfig = {
  sid: TWILIO_SID || '',
  authToken: TWILIO_AUTH_TOKEN || '',
  from: TWILIO_NUMBER || '',
};
