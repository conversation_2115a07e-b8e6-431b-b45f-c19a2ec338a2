const {
  DEFAULT_LIKE_WEIGHT,
  DEFAULT_COMMENT_WEIGHT,
  DEFAULT_SHARE_WEIGHT,
  TIMELINE_DEFAULT_LIMIT,
  LIKER_INFO_LIMIT,
  LIKERS_INFO_API_MAX_LIMIT,
  COMMENT_LIMIT
} = process.env;

export const reelsConfig = {
  like_weight: parseInt(DEFAULT_LIKE_WEIGHT) || 5,
  comment_weight: parseFloat(DEFAULT_COMMENT_WEIGHT) || 10,
  share_weight: parseInt(DEFAULT_SHARE_WEIGHT) || 15,
  timelineDefaultLimit: parseInt(TIMELINE_DEFAULT_LIMIT) || 20,
  likersInfoLimit: parseInt(LIKER_INFO_LIMIT) || 5,
  commentsLimit: parseInt(COMMENT_LIMIT) || 2,
  likersInfoApiMaxLimit:parseInt(LIKERS_INFO_API_MAX_LIMIT) || 100
};

export const buddyAffinityConfig = {
  like_weight: parseInt(DEFAULT_LIKE_WEIGHT) || 5,
  comment_weight: parseFloat(DEFAULT_COMMENT_WEIGHT) || 10,
  share_weight: parseInt(DEFAULT_SHARE_WEIGHT) || 15,
};
