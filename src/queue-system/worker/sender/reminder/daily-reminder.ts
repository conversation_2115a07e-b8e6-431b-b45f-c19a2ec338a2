import { Injectable, Module, OnApplicationBootstrap } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';
import { FCMSenderPayload } from 'src/queue-system/types';

class FcmServiceHelper extends FcmService {}

@Injectable()
export class DailyReminderSenderQueue implements OnApplicationBootstrap {
  async onApplicationBootstrap() {
    try {
      await (
        await QueueInstance
      ).consume(NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE);
    } catch (error) {
      console.log(error.message);
    }
  }

  static async sender(payload: FCMSenderPayload): Promise<void> {


    try {
      const { title, body, token, topic, isHighPriority, data, documentId, task } = payload;

      // Handle topic messaging for reminders (broadcast to all subscribed users)
      if (task === 'topic-sender' && topic && body) {


        // Send to FCM topic (broadcasts to all subscribed users)
        const result = await new FcmServiceHelper().sendToTopic({
          title,
          body,
          documentId,
          data,
          topic,
        });

        // Log actual FCM success with message ID
        if (result?.success && result?.messageId) {
          console.log(`✅ Reminder notification sent successfully: "${title}"`);
        } else {
          console.error(`❌ Reminder notification failed: "${title}" | Error: ${result?.error || 'Unknown error'}`);
        }


      }
      // Individual notification (fallback support)
      else if (token && body) {


        const result = await new FcmServiceHelper().sendToIndividual({
          token,
          title,
          body,
          documentId,
          data,
          isHighPriority,
          ttl: 600,
        });

        // Log individual notification result
        if (result) {
          console.log(`✅ Individual reminder notification sent successfully: "${title}" to token ${token.substring(0, 20)}...`);
        } else {
          console.error(`❌ Individual reminder notification failed: "${title}" to token ${token.substring(0, 20)}...`);
        }
      }
    } catch (error: any) {
      // Silent error handling
    }
  }
}

@Module({
  providers: [DailyReminderSenderQueue],
})
export class DailyReminderSenderModule {}
