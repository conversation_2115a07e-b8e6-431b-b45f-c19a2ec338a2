/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class RolesGuard extends AuthGuard('jwt') {
  constructor(private roles: string[] | null) {
    super();
  }

  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: string) {
    // console.log('user', user)
    if (!this.roles) {
      return user || null;
    }

    // console.log('user undecoded from jwt token',user)

    if (!user) {
      throw new UnauthorizedException(
        'Sorry! You are not a valid user for this action.',
      );
    }

    const role = user.role;
   
    const doesRoleMatch = this.roles.some((r) => r === role);
    if (!doesRoleMatch) {
      throw new UnauthorizedException(
        'Sorry! You are not a valid user for this action.',
      );
    }

   
    return user;
  }
}

@Injectable()
export class OptionalGuard extends AuthGuard('jwt') {
  constructor(private roles: string[] | null) {
    super();
  }

  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: string) {
    if (!this.roles) {
      return user || null;
    }

    if (!user) {
      return null;
    }

    const role = user.role;

    console.log(role)
    const doesRoleMatch = this.roles.some((r) => r === role);
    if (!doesRoleMatch) {
      throw new UnauthorizedException(
        'Sorry! You are not a valid user for this action.',
      );
    }
    
    return user;
  }
}



