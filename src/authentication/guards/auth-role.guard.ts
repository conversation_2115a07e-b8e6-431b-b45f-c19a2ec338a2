
import { ExecutionContext, Injectable, CanActivate, } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { SetMetadata } from '@nestjs/common';
import { throwForbiddenErrIf, throwUnauthorizedErrIf } from 'src/internal/exception/api.exception.ext';

/**
 * This guard can be used to verify authentication only. This guard
 * doesn't verify the role. Use this guard carefully. Using {@link OnlyRoleGuard}
 * with a combination of this class would be a good choice probably.
 */
@Injectable()
export class OnlyAuthGuard extends AuthGuard('jwt') { }

/**
 * This guard can be used to verify roles only. This guard doesn't verify
 * the authentication. Use this guard carefully. Using {@link OnlyAuthGuard}
 * with a combination of this class would be a good choice probably.
 */
@Injectable()
export class OnlyRoleGuard implements CanActivate {
  constructor(private reflector: Reflector) { }

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.get<Role[]>('role_information', context.getHandler());

    if (!roles) {  // If no roles are defined, allow the request
      return true; // Cause this end point may be public
    }

    const request = context.switchToHttp().getRequest();

    // if user is not valid then we will send unauthorizaed error
    const user = request.user;
    throwUnauthorizedErrIf(!user, "You are not authorized", "UNAUTHORIZED");

    // if role doesn't match then we will send forbidden error
    const roleMatched = roles.some(role => user.role === role);
    throwForbiddenErrIf(!roleMatched, "You don't have permission for this API", "FORBIDDEN");

    return true;
  }
}

/**
 * This is the tag to define endpoint roles. This tag will be used by {@link AuthAndRoleGuard}
 * to verify the authentication and role information. A list of possible values can be found
 * inside enum {@link Role}. This function will set the metadata information of roles and pass
 * it to the {@link AuthAndRoleGuard} so that it can verify required role accessibility. 
 * @param roles a list of {@link Role} to define accessibility of REST endpoints.
 * @returns 
 */
export const Roles = (...roles: Role[]) => SetMetadata('role_information', roles);

/**
 * Possible values of REST endpoints users. This class will be used by {@link AuthAndRoleGuard}
 * and {@link Roles} classes. Check these to know more.
 */
export enum Role {
  Admin = 'admin',
  User = 'user',
}