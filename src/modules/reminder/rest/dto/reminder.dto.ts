import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateReminderDto {
  @ApiProperty({
    description: 'Title of the daily reminder (sent to all users)',
    example: 'Daily Workout Reminder',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Message content of the daily reminder (sent to all users)',
    example: 'Time to do your daily workout! Stay fit with Fitsomnia!',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Time for the reminder in 24-hour format (HH:MM)',
    example: '08:00',
  })
  @IsString()
  @IsNotEmpty()
  time: string;

  @ApiProperty({
    description: 'Whether the reminder is active',
    example: true,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: 'Whether the reminder repeats on specific days',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isRepeating?: boolean;

  @ApiProperty({
    description: 'Days of the week when the reminder should repeat',
    example: ['monday', 'wednesday', 'friday'],
    default: [],
  })
  @IsArray()
  @IsOptional()
  repeatDays?: string[];
}

export class UpdateReminderDto {
  @ApiProperty({
    description: 'Title of the daily reminder (sent to all users)',
    example: 'Daily Workout Reminder',
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'Message content of the daily reminder (sent to all users)',
    example: 'Time to do your daily workout! Stay fit with Fitsomnia!',
  })
  @IsString()
  @IsOptional()
  message?: string;

  @ApiProperty({
    description: 'Time for the reminder in 24-hour format (HH:MM)',
    example: '08:00',
  })
  @IsString()
  @IsOptional()
  time?: string;

  @ApiProperty({
    description: 'Whether the reminder is active',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: 'Whether the reminder repeats on specific days',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isRepeating?: boolean;

  @ApiProperty({
    description: 'Days of the week when the reminder should repeat',
    example: ['monday', 'wednesday', 'friday'],
  })
  @IsArray()
  @IsOptional()
  repeatDays?: string[];
}

export class ReminderResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty({
    description: 'Admin ID who created this daily reminder',
  })
  userId: string;

  @ApiProperty({
    description: 'Title of the daily reminder sent to all users',
  })
  title: string;

  @ApiProperty({
    description: 'Message content of the daily reminder sent to all users',
  })
  message: string;

  @ApiProperty()
  time: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  isRepeating: boolean;

  @ApiProperty()
  repeatDays: string[];

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class GetRemindersResponseDto {
  @ApiProperty({
    type: [ReminderResponseDto],
    description: 'List of all daily reminders created by admins',
  })
  data: ReminderResponseDto[];
}
