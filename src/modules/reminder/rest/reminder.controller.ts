import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { ReminderService } from '../services/reminder.service';
import { ReminderSchedulerService } from '../services/reminder-scheduler.service';
import { NotificationHelperService } from '../../notification/services/helper.notification.service';
import {
  CreateReminderDto,
  GetRemindersResponseDto,
  ReminderResponseDto,
  UpdateReminderDto,
} from './dto/reminder.dto';

@ApiTags('Reminders')
@Controller('reminders')
export class ReminderController {
  constructor(
    private readonly reminderService: ReminderService,
    private readonly reminderSchedulerService: ReminderSchedulerService,
    private readonly notificationHelperService: NotificationHelperService,
  ) {}

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Post()
  @ApiOperation({ summary: 'Create a new daily reminder (Admin only)' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Daily reminder created successfully',
    type: ReminderResponseDto,
  })
  async createReminder(
    @Req() request: any,
    @Body() createReminderDto: CreateReminderDto,
  ) {
    const admin = await request.user;
    const reminder = await this.reminderService.createReminder(
      admin.id,
      createReminderDto,
    );
    return reminder;
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get()
  @ApiOperation({ summary: 'Get all daily reminders (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminders retrieved successfully',
    type: GetRemindersResponseDto,
  })
  async getReminders() {
    const reminders = await this.reminderService.getAllReminders();
    return { data: reminders };
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get(':id')
  @ApiOperation({ summary: 'Get a daily reminder by ID (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminder retrieved successfully',
    type: ReminderResponseDto,
  })
  async getReminderById(@Param('id') id: string) {
    return await this.reminderService.getReminderById(id);
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Put(':id')
  @ApiOperation({ summary: 'Update a daily reminder (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminder updated successfully',
    type: ReminderResponseDto,
  })
  async updateReminder(
    @Param('id') id: string,
    @Body() updateReminderDto: UpdateReminderDto,
  ) {
    return await this.reminderService.updateReminder(id, updateReminderDto);
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a daily reminder (Admin only)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reminder deleted successfully',
  })
  async deleteReminder(@Param('id') id: string) {
    const deleted = await this.reminderService.deleteReminder(id);
    return { success: deleted };
  }
}
