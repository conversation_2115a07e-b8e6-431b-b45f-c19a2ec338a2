import { Injectable, OnApplicationBootstrap } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ReminderService } from './reminder.service';

@Injectable()
export class ReminderSchedulerService implements OnApplicationBootstrap {
  constructor(private readonly reminderService: ReminderService) {}

  /**
   * Subscribe all active users to reminder topic when application starts
   */
  async onApplicationBootstrap() {
    try {
      await this.reminderService.subscribeAllUsersToReminderTopic();
    } catch (error) {
      console.error('Error initializing reminder scheduler:', error.message);
    }
  }

  /**
   * Cron job that runs every minute to check for reminders that need to be sent
   * Simple implementation - just check and send reminders
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async checkAndSendReminders() {
    await this.reminderService.sendReminderNotifications();
  }
}
