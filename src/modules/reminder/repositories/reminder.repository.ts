import { Injectable } from '@nestjs/common';
import { ReminderModel } from 'src/database/reminder/reminder.model';
import { <PERSON>minder } from 'src/entity/reminder';
import { CreateReminderDto, UpdateReminderDto } from '../rest/dto/reminder.dto';

@Injectable()
export class ReminderRepository {
  async createReminder(
    adminId: string,
    reminderData: CreateReminderDto,
  ): Promise<Reminder> {
    try {
      const reminder = new ReminderModel({
        userId: adminId, // Store admin ID who created the reminder
        ...reminderData,
      });
      return await reminder.save();
    } catch (error) {
      console.error('Error creating reminder:', error.message);
      return null;
    }
  }

  async findReminderById(id: string): Promise<Reminder> {
    try {
      return await ReminderModel.findOne({ id }).lean();
    } catch (error) {
      console.error('Error finding reminder by ID:', error.message);
      return null;
    }
  }

  async findRemindersByUserId(userId: string): Promise<Reminder[]> {
    try {
      return await ReminderModel.find({ userId }).lean();
    } catch (error) {
      console.error('Error finding reminders by user ID:', error.message);
      return [];
    }
  }

  async findAllReminders(): Promise<Reminder[]> {
    try {
      return await ReminderModel.find({}).lean();
    } catch (error) {
      console.error('Error finding all reminders:', error.message);
      return [];
    }
  }

  async updateReminder(
    id: string,
    updateData: UpdateReminderDto,
  ): Promise<Reminder> {
    try {
      return await ReminderModel.findOneAndUpdate(
        { id },
        { $set: updateData },
        { new: true },
      ).lean();
    } catch (error) {
      console.error('Error updating reminder:', error.message);
      return null;
    }
  }

  async deleteReminder(id: string): Promise<boolean> {
    try {
      const result = await ReminderModel.deleteOne({ id });
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting reminder:', error.message);
      return false;
    }
  }

  async findActiveRemindersForTime(time: string): Promise<Reminder[]> {
    try {
      const currentDay = new Date()
        .toLocaleDateString('en-US', { weekday: 'long' })
        .toLowerCase(); // e.g., 'monday'

      // Find admin-created reminders that match the time and are active
      const reminders = await ReminderModel.find({
        time,
        isActive: true,
        $or: [
          { isRepeating: false }, // Non-repeating reminders
          {
            isRepeating: true,
            repeatDays: { $in: [currentDay] }, // Repeating reminders for today
          },
        ],
      }).lean();

      return reminders;
    } catch (error) {
      console.error('Error finding active reminders for time:', error.message);
      return [];
    }
  }
}
