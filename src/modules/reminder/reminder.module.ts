import { Modu<PERSON> } from '@nestjs/common';
import { <PERSON>minder<PERSON>ontroller } from './rest/reminder.controller';
import { ReminderService } from './services/reminder.service';
import { ReminderRepository } from './repositories/reminder.repository';
import { ReminderSchedulerService } from './services/reminder-scheduler.service';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [NotificationModule],
  controllers: [ReminderController],
  providers: [
    ReminderService,
    ReminderRepository,
    ReminderSchedulerService,
  ],
  exports: [ReminderService],
})
export class ReminderModule {}
