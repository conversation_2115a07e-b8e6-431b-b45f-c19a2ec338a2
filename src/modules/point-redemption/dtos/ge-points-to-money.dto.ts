import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { IsNotEmpty, IsObject } from "class-validator";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";

export class GetPointsToMoneyResponseDto{
    @Expose()
    @ApiProperty({ required: true, type: Number })
    amountToWithdraw: number;
        
}

export class GetPointsToMoneyResponseDtoSuccessResponseDto implements ServiceSuccessResponse {

  @Expose()
  @ApiProperty({ required: true, type: GetPointsToMoneyResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: GetPointsToMoneyResponseDto;

}