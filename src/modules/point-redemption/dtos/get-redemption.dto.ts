import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { IsNotEmpty, IsObject } from "class-validator";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";

export class GetRedemptionPointResponseDto{
    @Expose()
    @ApiProperty({ required: true, type: Number })
    conversionRate: number;
        
}

export class GetRedemptionPointResponseDtoSuccessResponseDto implements ServiceSuccessResponse {

  @Expose()
  @ApiProperty({ required: true, type: GetRedemptionPointResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: GetRedemptionPointResponseDto;

}