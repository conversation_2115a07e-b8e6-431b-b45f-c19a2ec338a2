import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { IsNotEmpty, IsNumber, IsObject, IsString } from "class-validator";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";

export class CreateRedemptionPointConversionRequestDto {

    @ApiProperty({ required: true, type: Number, description: 'Amount in taka per point (e.g., 0.5, 1, 2)', example: 1 })
    @IsNotEmpty()
    @IsNumber()
    conversionRate: number;
}



export class CreateRedemptionPointConversionResponseDto {

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  conversionRate: number;
    
  @Expose()
  @ApiProperty({ required: false, type: Boolean })
  isActive: boolean;

}

export class  CreateRedemptionPointConversionSuccessResponseDto implements ServiceSuccessResponse {

  @Expose()
  @ApiProperty({ required: true, type: CreateRedemptionPointConversionResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: CreateRedemptionPointConversionResponseDto;

}