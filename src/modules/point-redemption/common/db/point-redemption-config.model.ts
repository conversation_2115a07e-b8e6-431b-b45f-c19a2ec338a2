import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { PointRedemptionConfigEntity } from '../entities/point-redemption-config.entity';

const pointRedemptionConfigSchema = new Schema<PointRedemptionConfigEntity>(
    {
        id: {
            type: String,
            default: () => randomUUID(),
            unique: true,
        },
        conversionRate: {
            type: Number,
            required: true
        },
        isActive: {
            type: Boolean,
            required: true,
            default: false,
        },
    },
    {
        timestamps: true,
        versionKey: false,
      },
)

const PointRedemptionConfigModel = model<PointRedemptionConfigEntity>('point-redemption-config', pointRedemptionConfigSchema);

export { PointRedemptionConfigModel };

