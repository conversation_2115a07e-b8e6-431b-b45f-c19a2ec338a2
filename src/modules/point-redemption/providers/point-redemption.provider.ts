import { Injectable } from '@nestjs/common';
import { PointRedemptionConfigRepository } from '../repositories/point-redemption-config.repository';

@Injectable()
export class PointRedemptionProvider {
  // Provider methods and logic go here
  constructor(private readonly repository: PointRedemptionConfigRepository) {}

  async getActiveConfig() {
    return await this.repository.getActiveConfig();
  }
}
