import { HttpStatus, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from 'src/internal/casting/object.casting';
import { throwNotFoundErr } from 'src/internal/exception/api.exception.ext';
import { APIException } from '../../../internal/exception/api.exception';
import { INVALID_INPUT } from '../common/const/point-redemption.const';
import { CreateRedemptionPointConversionResponseDto, CreateRedemptionPointConversionSuccessResponseDto } from '../dtos/create-redemption.dto';
import { GetPointsToMoneyResponseDto, GetPointsToMoneyResponseDtoSuccessResponseDto } from '../dtos/ge-points-to-money.dto';
import { GetRedemptionPointResponseDto, GetRedemptionPointResponseDtoSuccessResponseDto } from '../dtos/get-redemption.dto';
import { PointRedemptionConfigRepository } from '../repositories/point-redemption-config.repository';
import { UserPointsProvider } from './../../user-points/providers/user-points.provider';
// import { PointRedemptionRequestRepository } from '../repositories/point-redemption-request.repository';

@Injectable()
export class PointRedemptionService {
  constructor(
    private readonly pointRedemptionConfigRepository: PointRedemptionConfigRepository,
    // private readonly pointRedemptionRequestRepository: PointRedemptionRequestRepository,
    private readonly userPointsProvider: UserPointsProvider,
     private readonly helper: Helper,
  ) {}

  // Admin operations
 
  async createOrUpdateConfig(conversionRate: number): Promise<CreateRedemptionPointConversionSuccessResponseDto> {
    if (conversionRate <= 0) {
      throwNotFoundErr(conversionRate <= 0, "Conversion rate must be greater than 0", INVALID_INPUT);
    }

    const config = await this.pointRedemptionConfigRepository.createOrUpdateConfig(conversionRate);

    const responseDto = deepCasting(CreateRedemptionPointConversionResponseDto, config);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getActiveConfig(): Promise<GetRedemptionPointResponseDtoSuccessResponseDto> {
    const config = await this.pointRedemptionConfigRepository.getActiveConfig();
    const responseDto = deepCasting(GetRedemptionPointResponseDto, config);
    
    if (!responseDto) {
      throw new APIException(
        'No active conversion rate configuration found',
        'NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    return this.helper.serviceResponse.successResponse(responseDto);
  }


  async getPointsToMoneyConversion(userId: string): Promise<GetPointsToMoneyResponseDtoSuccessResponseDto> { 
    const userPoint = await this.userPointsProvider.getUserPointsById(userId)
    const config = await this.pointRedemptionConfigRepository.getActiveConfig();

    const amountToWithdraw = userPoint.points *config.conversionRate
    const responseDto = deepCasting(GetPointsToMoneyResponseDto, { amountToWithdraw });
      if (!responseDto) {
      throw new APIException(
        'Money is Not available',
        'NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }
    return this.helper.serviceResponse.successResponse(responseDto);
  }
}
