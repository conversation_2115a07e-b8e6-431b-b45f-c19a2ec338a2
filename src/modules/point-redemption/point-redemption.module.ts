import { forwardRef, Module } from '@nestjs/common';
import { UserPointsModule } from '../user-points/user-points.module';
import { PointRedemptionProvider } from './providers/point-redemption.provider';
import { PointRedemptionConfigRepository } from './repositories/point-redemption-config.repository';
import { AdminPointRedemptionController } from './rest/admin-point-redemption.controller';
import { UserPointRedemptionController } from './rest/user-point-redemption.controller';
import { PointRedemptionService } from './services/point-redemption.service';

@Module({
  imports: [
    forwardRef(()=> UserPointsModule),
  ],
  controllers: [AdminPointRedemptionController, UserPointRedemptionController],
  providers: [
    PointRedemptionService,
    PointRedemptionConfigRepository,
    PointRedemptionProvider,
    // PointRedemptionRequestRepository,
  ],
  exports: [PointRedemptionService, PointRedemptionProvider],
})
export class PointRedemptionModule {}
