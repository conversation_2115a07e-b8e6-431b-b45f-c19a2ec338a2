import { Injectable } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import {
  IUserChallengeStatusEnum,
  IUserPointsSources,
  IUserPointsTransTypeEnum,
  NotificationCategory,
  NotificationModule,
  NotificationType,
} from 'models';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { UserPointsService } from 'src/modules/user-points/services';
import { UserRepository } from 'src/modules/user/repositories';
import { BaseChallengeRepository } from '../repositories';
import { UserChallengeRepository } from '../repositories/user.challenge.repository';

@Injectable()
export class ChallengeApprovalService {
  constructor(
    private readonly baseChallengeRepository: BaseChallengeRepository,
    private readonly userChallengeRepository: UserChallengeRepository,
    private readonly userPointsService: UserPointsService,
    private readonly userRepository: UserRepository,
    private readonly notificationHelperService: NotificationHelperService,
  ) {}

  /**
   * Cron job that runs at midnight (12:00 AM) every day to automatically approve all pending challenges
   * This eliminates the need for manual admin review and awards points to users immediately
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  // @Cron(CronExpression.EVERY_MINUTE)
  async autoApprovePendingChallenges() {
    console.log('Running cron job for auto-approving pending challenges');

    try {
      // Find all pending challenges
      // const pendingChallenges = await UserChallengeModel.find({
      //   lastStatus: IUserChallengeStatusEnum.PENDING
      // });

      const pendingChallenges =
        await this.userChallengeRepository.findPendingChallenges();
      // console.log('pending challenges', pendingChallenges)

      console.log(
        `Found ${pendingChallenges.length} pending challenges to approve`,
      );

      // Process each pending challenge
      for (const challenge of pendingChallenges) {
        // Update challenge status to COMPLETED
        const setData = {
          lastStatus: IUserChallengeStatusEnum.COMPLETED,
        };

        const query = {
          id: challenge.id,
          lastStatus: IUserChallengeStatusEnum.PENDING,
          // lastStatus: IUserChallengeStatusEnum.IN_PROGRESS
        };

        const updated =
          await this.baseChallengeRepository.reviewAndChangeStatus(
            query,
            setData,
          );

        if (updated && updated.points > 0) {
          // Award points to user
          const updatePoints = await this.userPointsService.updatePoints(
            updated.userId,
            {
              type: IUserPointsTransTypeEnum.EARNED,
              points: updated.points,
              pointSourceType: IUserPointsSources.Challenges,
              pointSourceId: challenge.challengeId,
              pointSourceName: challenge.challengeName || '',
            },
          );

          if (updatePoints) {
            // Get user details for notification
            const user = await this.userRepository.findUser({
              id: updated.userId,
            });

            // Send notification to user
            const payload = {
              recipient: user,
              createdBy: {
                name: 'system',
              },
              title: 'Your challenge has been approved!',
              content: `Your challenge has been automatically approved and you've earned ${updated.points} points!`,
              module: NotificationModule.CHALLENGE,
              type: NotificationType.CHALLENGE_STATUS,
              category: NotificationCategory.CHALLENGE_STATUS,
              documentId: updated.id,
            };

            await this.notificationHelperService.sendNotifications(payload);
            console.log(
              `Successfully approved challenge ${updated.id} for user ${updated.userId}`,
            );
          } else {
            console.error(
              `Failed to update points for challenge ${challenge.id}, user ${challenge.userId}`,
            );
          }
        } else {
          console.error(
            `Failed to update challenge status for ${challenge.id}`,
          );
        }
      }

      console.log('Finished auto-approving pending challenges');
    } catch (error) {
      console.error(
        'Error in auto-approving pending challenges:',
        error.message,
      );
    }
  }
}
