import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { bkashConfig } from 'config/bkash';
import { RedisCacheHelper } from 'src/cache/helper';
import { BkashTokenData } from '../entities/token.entity';

@Injectable()
export class BkashTokenService {
  private static readonly REDIS_KEY = 'bKashToken';

  private async getBkashGrantToken(): Promise<string> {
    try {
      const response = await axios.post(
        `${bkashConfig.bkashBaseUrl}/tokenized/checkout/token/grant`,
        {
          app_key: bkashConfig.bkashAppKey,
          app_secret: bkashConfig.bkashAppSecret,
        },
        {
          headers: {
            username: bkashConfig.bkashUsername,
            password: bkashConfig.bkashPassword,
          },
        },
      );

      if (response.data.statusCode === '0000') {
        const tokenData: BkashTokenData = {
          grantToken: response.data.id_token,
          refreshToken: response.data.refresh_token,
          tokenIssueTime: Date.now(),
        };
        await RedisCacheHelper.setData<BkashTokenData>(
          BkashTokenService.REDIS_KEY,
          tokenData,
        );
        return tokenData.grantToken;
      }
      throw new Error('Failed to get bKash grant token');
    } catch (error) {
      throw new Error(`bKash grant token error: ${error.message}`);
    }
  }

  private async getTokensFromCache(): Promise<BkashTokenData | null> {
    const data: BkashTokenData | null = await RedisCacheHelper.getData(BkashTokenService.REDIS_KEY);
    return data;
  }



  private async refreshBkashToken(): Promise<string> {
    try {
      const tokenData = await this.getTokensFromCache();
      const response = await axios.post(
        `${bkashConfig.bkashBaseUrl}/tokenized/checkout/token/refresh`,
        {
          app_key: bkashConfig.bkashAppKey,
          app_secret: bkashConfig.bkashAppSecret,
          refresh_token: tokenData?.refreshToken,
        },
        {
          headers: {
            username: bkashConfig.bkashUsername,
            password: bkashConfig.bkashPassword,
          },
        },
      );

      if (response.data.statusCode === '0000') {
        const newTokenData: BkashTokenData = {
          grantToken: response.data.id_token,
          refreshToken: response.data.refresh_token,
          tokenIssueTime: Date.now(),
        };
        await RedisCacheHelper.setData<BkashTokenData>(
          BkashTokenService.REDIS_KEY,
          newTokenData,
        );
        return newTokenData.grantToken;
      }
      throw new Error('Failed to refresh bKash token');
    } catch (error) {
      // If refresh token has expired or refresh fails, fallback to getting a new grant token
      console.warn(`bKash token refresh failed: ${error.message}. Falling back to new grant token.`);
      try {
        return await this.getBkashGrantToken();
      } catch (grantError) {
        throw new Error(`bKash token refresh and grant token fallback both failed: ${grantError.message}`);
      }
    }
  }

  /**
   * Gets a valid bKash token, either from cache or by requesting a new one
   * Tokens are valid for 60 minutes. If token is older than 55 minutes, refresh it
   */
  async getBkashToken(): Promise<string> {
    const TOKEN_EXPIRY_MS = 55 * 60 * 1000; // 55 minutes in milliseconds
    const currentTime = Date.now();

    const tokenData = await this.getTokensFromCache();

    // If no token exists, get a new grant token
    if (!tokenData || !tokenData.grantToken || !tokenData.refreshToken) {
      return this.getBkashGrantToken();
    }

    // If token is about to expire, refresh it
    if (currentTime - tokenData.tokenIssueTime > TOKEN_EXPIRY_MS) {
      return this.refreshBkashToken();
    }

    return tokenData.grantToken;
  }
}