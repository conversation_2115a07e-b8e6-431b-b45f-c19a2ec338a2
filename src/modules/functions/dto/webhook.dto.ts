import { IsEnum, IsNotEmpty, IsString } from 'class-validator';

export enum WebhookStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

export enum WebhookType {
  POST_VIDEO = 'POST_VIDEO',
}

export class WebhookDto {
  @IsEnum(WebhookStatus)
  @IsNotEmpty()
  status: WebhookStatus;

  @IsString()
  @IsNotEmpty()
  type: WebhookType;

  @IsString()
  @IsNotEmpty()
  url: string;
}

export class FunctionWebhookQueryDto {
  @IsString()
  @IsNotEmpty()
  type: WebhookType;

  @IsEnum(WebhookStatus)
  @IsNotEmpty()
  status: WebhookStatus;

  @IsString()
  @IsNotEmpty()
  url: string;
}
