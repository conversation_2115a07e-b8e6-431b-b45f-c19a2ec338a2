import { Injectable } from '@nestjs/common';
import { redisCacheConfig } from 'config/cache';
import { RedisCacheHelper } from 'src/cache/helper';
import { PostService } from '../post/services';
import { WebhookDto, WebhookStatus, WebhookType } from './dto/webhook.dto';
import { FunctionsRepository } from './functions.repository';

@Injectable()
export class FunctionsService {
  constructor(
    private readonly functionsRepository: FunctionsRepository,
    private readonly postService: PostService
  ) {}
  async handleFunctionWebhook(webhookDto: WebhookDto) {
    console.log(webhookDto);
    const { url, type } = webhookDto;
    if (webhookDto.status === WebhookStatus.SUCCESS) {
      if (type === WebhookType.POST_VIDEO) {
        const newUrl = `hls/${url}/master.m3u8`;
        console.log('url', url);
        console.log('newUrl', newUrl);
        
        // Update the post in the database
        const updatedPost = await this.functionsRepository.updatePost(
          { 'videos.url': url },
          { videos: [{ url: newUrl }] }
        );
        
        // If post was found and updated, update its cache
        if (updatedPost && updatedPost.id) {
          console.log(`Updating cache for post: ${updatedPost.id}`);
          
          // Update the cache for this post
          const cacheKey = `${redisCacheConfig.post_channel}/${updatedPost.id}`;
          
          // First delete the old cache entry
          await RedisCacheHelper.deleteData(cacheKey);
          
          // Use the PostService's updateRedisCache method for comprehensive cache update
          await this.postService.updateRedisCache(updatedPost);
          
          console.log(`Cache updated for post: ${updatedPost.id}`);
        }
      }
    } else if (webhookDto.status === WebhookStatus.FAILED) {
        return;
    }
  }
}
