import { Module } from '@nestjs/common';
import { PostModule } from '../post/post.rest.module';
import { FunctionWebhookController } from './functions.controller';
import { FunctionsRepository } from './functions.repository';
import { FunctionsService } from './functions.service';

@Module({
  imports: [PostModule],
  controllers: [FunctionWebhookController],
  providers: [FunctionsService, FunctionsRepository],
})
export class FunctionsModule {}