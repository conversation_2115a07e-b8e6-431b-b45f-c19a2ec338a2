import { Injectable } from '@nestjs/common';
import { UpdatePostBody } from 'models/post/update.post.interface';
import { PostModel } from 'src/database/post/post.model';
import { Post } from 'src/entity/post';

@Injectable()
export class FunctionsRepository {
  async updatePost(
    query: Record<string, any>,
    data: UpdatePostBody,
  ): Promise<Post | null> {
    try {
      const post = await PostModel.findOneAndUpdate(
        query,
        { $set: data },
        { new: true },
      ).lean();
      console.log('post', post);
      return post;
    } catch (error) {
      console.log(error);
      return null;
    }
  }
}