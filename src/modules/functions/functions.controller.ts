import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { FunctionWebhookQueryDto } from './dto/webhook.dto';
import { FunctionsService } from './functions.service';

@ApiTags('Function Webhook')
@Controller('functions')
export class FunctionWebhookController {
  constructor(private readonly functionWebhookService: FunctionsService) {}

  @Get('webhook')
  async handleFunctionWebhook(
    @Query() queryDto: FunctionWebhookQueryDto
  ) {
    return this.functionWebhookService.handleFunctionWebhook(queryDto);
  }
}
