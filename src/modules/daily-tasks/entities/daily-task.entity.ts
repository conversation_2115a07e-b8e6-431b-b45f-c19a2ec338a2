export enum DailyTaskType {
  REACTIONS = 'REACTIONS',
  SPOT_FRIENDS = 'SPOT_FRIENDS',
  INVITE_FRIENDS = 'INVITE_FRIENDS',
}

export interface DailyTasksEntity {
  id?: string;
  title: string;
  description: string;
  taskType: DailyTaskType;
  targetCount: number;
  points: number;
  isActive: boolean;
}

export interface UserDailyTaskEntity {
  id?: string;
  userId: string;
  taskId: string;
  currentCount: number;
  isCompleted: boolean;
  completedAt?: Date;
  date: string; // YYYY-MM-DD format
}