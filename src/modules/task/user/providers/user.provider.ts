import { Injectable } from '@nestjs/common';
import { TaskType } from '../../common/entities/task.entity';
import {
  CompletionStatus,
  UserTaskEntity,
} from '../../common/entities/user-task.entity';
import { TaskRepositoryForUser } from '../repositories/user.repository';

@Injectable()
export class TaskProviderForUser {
  constructor(private readonly taskRepository: TaskRepositoryForUser) {}

  async createUserTask(
    userId: string,
    taskId: string,
  ): Promise<UserTaskEntity> {
    return await this.taskRepository.createUserTask(userId, taskId);
  }

  async getUserTaskById(id: string): Promise<UserTaskEntity | null> {
    return await this.taskRepository.findUserTaskById(id);
  }

  async getUserTaskByUserAndTaskId(
    userId: string,
    taskId: string,
  ): Promise<UserTaskEntity | null> {
    return await this.taskRepository.findUserTaskByUserAndTaskId(
      userId,
      taskId,
    );
  }

  async updateUserTaskProgress(
    id: string,
    progress: number,
    status?: CompletionStatus,
  ): Promise<UserTaskEntity | null> {
    return await this.taskRepository.updateUserTaskProgress(
      id,
      progress,
      status,
    );
  }

  async markUserTaskCompleted(id: string): Promise<UserTaskEntity | null> {
    return await this.taskRepository.markUserTaskCompleted(id);
  }

  async getUserTasks(
    userId: string,
    options?: {
      type?: TaskType;
    },
  ): Promise<UserTaskEntity[]> {
    return await this.taskRepository.findUserTasks(userId, options);
  }

  async getAllUserTasksByTaskId(taskId: string): Promise<UserTaskEntity[]> {
    return await this.taskRepository.findAllUserTasksByTaskId(taskId);
  }
}
