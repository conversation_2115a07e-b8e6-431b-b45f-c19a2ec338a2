import {
  Controller,
  Get,
  HttpStatus,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
import { TASK_API_FOR_USER } from '../../common/const/swagger.const';
import {
  GetUserTasksQueryDtoForUser,
  GetUserTasksSuccessResponseDtoForUser,
  GetUserTaskSuccessResponseDtoForUser,
} from '../dtos/user-get-task.dto';
import { TaskServiceForUser } from '../services/user.service';

@ApiTags(TASK_API_FOR_USER)
@UseGuards(new RolesGuard(['user']))
@ApiBearerAuth()
@Controller('tasks')
export class TaskControllerForUser {
  constructor(private readonly taskServiceForUser: TaskServiceForUser) {}

  @ApiResponse({
    description: 'Get user tasks',
    type: GetUserTasksSuccessResponseDtoForUser,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Get all tasks for current user',
    description:
      'Returns tasks filtered by status (active or completed) and sorted by creation date',
  })
  @Get()
  async getUserTasks(
    @UserInfo() user: User,
    @Query() query: GetUserTasksQueryDtoForUser,
  ) {
    return await this.taskServiceForUser.getUserTasks(user.id, query);
  }

  @ApiResponse({
    description: 'Get user task',
    type: GetUserTaskSuccessResponseDtoForUser,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get a specific task for current user' })
  @Get(':taskId')
  async getUserTask(@UserInfo() user: User, @Param('taskId') taskId: string) {
    return await this.taskServiceForUser.getUserTaskById(user.id, taskId);
  }
}
