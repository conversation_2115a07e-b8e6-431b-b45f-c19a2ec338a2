import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';
import {
  TaskRequirementType,
  TaskType,
} from '../../common/entities/task.entity';

export class GetTasksQueryDtoForAdmin {
  @ApiProperty({ enum: TaskType, required: false })
  @IsEnum(TaskType)
  @IsOptional()
  type?: TaskType;

  @ApiProperty({ enum: TaskRequirementType, required: false })
  @IsEnum(TaskRequirementType)
  @IsOptional()
  requirementType?: TaskRequirementType;

  @ApiProperty({ required: false })
  @IsOptional()
  isActive?: boolean;
}

export class GetTaskResponseDtoForAdmin {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  title: string;

  @Expose()
  @ApiProperty()
  description: string;

  @Expose()
  @ApiProperty({ enum: TaskType })
  type: TaskType;

  @Expose()
  @ApiProperty({ enum: TaskRequirementType })
  requirementType: TaskRequirementType;

  @Expose()
  @ApiProperty()
  targetValue: number;

  @Expose()
  @ApiProperty()
  points: number;

  @Expose()
  @ApiProperty()
  isActive: boolean;

  @Expose()
  @ApiProperty({ required: false })
  deadline?: Date;
}

export class GetTaskSuccessResponseDtoForAdmin {
  @ApiProperty({ type: GetTaskResponseDtoForAdmin })
  data: GetTaskResponseDtoForAdmin;
}

export class GetTasksSuccessResponseDtoForAdmin {
  @ApiProperty({ type: [GetTaskResponseDtoForAdmin] })
  data: GetTaskResponseDtoForAdmin[];
}
