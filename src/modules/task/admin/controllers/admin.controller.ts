import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { TASK_API_FOR_ADMIN } from '../../common/const/swagger.const';
import {
  CreateTaskRequestDtoForAdmin,
  CreateTaskSuccessResponseDtoForAdmin,
} from '../dtos/admin-create-task.dto';
import {
  GetTasksQueryDtoForAdmin,
  GetTasksSuccessResponseDtoForAdmin,
  GetTaskSuccessResponseDtoForAdmin,
} from '../dtos/admin-get-task.dto';
import {
  UpdateTaskRequestDtoForAdmin,
  UpdateTaskSuccessResponseDtoForAdmin,
} from '../dtos/admin-update-task.dto';
import { TaskServiceForAdmin } from '../services/admin.service';

@ApiTags(TASK_API_FOR_ADMIN)
@UseGuards(new RolesGuard(['admin']))
@ApiBearerAuth()
@Controller('admin/tasks')
export class TaskControllerForAdmin {
  constructor(private readonly taskServiceForAdmin: TaskServiceForAdmin) {}

  @ApiResponse({
    description: 'Create a Task Response',
    type: CreateTaskSuccessResponseDtoForAdmin,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({ summary: 'Create a new task' })
  @Post()
  async createTask(@Body() taskData: CreateTaskRequestDtoForAdmin) {
    return await this.taskServiceForAdmin.createTask(taskData);
  }

  @ApiResponse({
    description: 'Get all tasks',
    type: GetTasksSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get all tasks' })
  @Get()
  async getAllTasks(@Query() query: GetTasksQueryDtoForAdmin) {
    return await this.taskServiceForAdmin.getAllTasks(query);
  }

  @ApiResponse({
    description: 'Get single task',
    type: GetTaskSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get a specific task by ID' })
  @Get(':id')
  async getTask(@Param('id') id: string) {
    return await this.taskServiceForAdmin.getTaskById(id);
  }

  @ApiResponse({
    description: 'Update task',
    type: UpdateTaskSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Update a task' })
  @Patch(':id')
  async updateTask(
    @Param('id') id: string,
    @Body() updateData: UpdateTaskRequestDtoForAdmin,
  ) {
    return await this.taskServiceForAdmin.updateTask(id, updateData);
  }

  @ApiResponse({
    description: 'Delete task',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Delete a task' })
  @Delete(':id')
  async deleteTask(@Param('id') id: string) {
    return await this.taskServiceForAdmin.deleteTask(id);
  }
}
