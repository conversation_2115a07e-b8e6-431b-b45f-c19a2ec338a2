import { Injectable } from '@nestjs/common';
import {
  TaskEntity,
  TaskRequirementType,
  TaskType,
} from '../../common/entities/task.entity';
import { TaskRepositoryForAdmin } from '../repositories/admin.repository';

@Injectable()
export class TaskProviderForAdmin {
  constructor(private readonly taskRepository: TaskRepositoryForAdmin) {}

  async createTask(taskData: TaskEntity): Promise<TaskEntity> {
    return await this.taskRepository.createTask(taskData);
  }

  async getTaskById(id: string): Promise<TaskEntity | null> {
    return await this.taskRepository.findTaskById(id);
  }

  async updateTask(
    id: string,
    updateData: Partial<TaskEntity>,
  ): Promise<TaskEntity | null> {
    return await this.taskRepository.updateTask(id, updateData);
  }

  async deleteTask(id: string): Promise<boolean> {
    return await this.taskRepository.deleteTask(id);
  }

  async getTasks(options?: {
    type?: TaskType;
    requirementType?: TaskRequirementType;
    isActive?: boolean;
  }): Promise<TaskEntity[]> {
    return await this.taskRepository.findTasks(options);
  }
}
