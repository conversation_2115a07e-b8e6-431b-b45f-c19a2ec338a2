import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import {
  TaskEntity,
  TaskRequirementType,
  TaskType,
} from '../entities/task.entity';

const TaskSchema = new Schema<TaskEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: Object.values(TaskType),
      default: TaskType.DAILY,
    },
    requirementType: {
      type: String,
      enum: Object.values(TaskRequirementType),
      required: true,
    },
    targetValue: {
      type: Number,
      required: true,
    },
    points: {
      type: Number,
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    deadline: {
      type: Date,
      required: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const TaskEntityModel = model<TaskEntity>('task_entity', TaskSchema);

export { TaskEntityModel };
