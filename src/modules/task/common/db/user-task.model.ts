import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { CompletionStatus, UserTaskEntity } from '../entities/user-task.entity';

const UserTaskSchema = new Schema<UserTaskEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
    },
    taskId: {
      type: String,
      required: true,
    },
    progress: {
      type: Number,
      default: 0,
    },
    completionStatus: {
      type: String,
      enum: Object.values(CompletionStatus),
      default: CompletionStatus.NOT_STARTED,
    },
    pointsAwarded: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const UserTaskEntityModel = model<UserTaskEntity>(
  'user_task_entity',
  UserTaskSchema,
);

export { UserTaskEntityModel };
