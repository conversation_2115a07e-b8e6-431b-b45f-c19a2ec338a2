export enum TaskType {
  DAILY = 'daily',
  SPECIAL = 'special',
}

export enum TaskRequirementType {
  POST_CREATION = 'post_creation',
  POST_REACTION = 'post_reaction',
  PROFILE_COMPLETION = 'profile_completion',
  FRIEND_INVITATION = 'friend_invitation',
  COACH_PROFILE = 'coach_profile',
  POST_COMMENT = 'post_comment',
  DIET_CREATION = 'diet_creation',
  MEAL_TRACKING = 'meal_tracking',
  WATER_CONSUMPTION = 'water_consumption',
  SPOT_PROFILE_CREATION = 'spot_profile_creation',
  SPOT_REQUEST = 'spot_request',
  // Add more requirement types as needed
}

export interface TaskEntity {
  id: string;
  title: string;
  description: string;
  type: TaskType;
  requirementType: TaskRequirementType;
  targetValue: number;
  points: number;
  isActive: boolean;
  deadline?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}
