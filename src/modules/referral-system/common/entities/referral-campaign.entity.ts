
export class ReferralCampaignMediaItem {
  type: ReferralCampaignMediaItemType;
  url: string;
}

export enum ReferralCampaignMediaItemType {
  VIDEO = "video",
  IMAGE = "image"
}

// campaignType

export enum CampaignStatus {
    DRAFT = 'DRAFT',
    SCHEDULED = 'SCHEDULED',
    ACTIVE = 'ACTIVE',
    PAUSED = 'PAUSED',
    COMPLETED = 'COMPLETED',
    CANCELLED = 'CANCELLED'
}

export enum TaskStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED',
    EXPIRED = 'EXPIRED'
}

export enum TaskEventType {
    COMPLETE_ONBOARDING = 'COMPLETE_ONBOARDING',
    SUBSCRIBE_TO_PROGRAM = 'SUBSCRIBE_TO_PROGRAM',
    COMPLETE_WORKOUT = 'COMPLETE_WORKOUT',
    JOIN_CHALLENGE = 'JOIN_CHALLENGE'
}

export interface TaskConfig {
    count?: number;         // For counting type tasks (e.g., complete 3 workouts)
    duration?: number;      // For duration based tasks (e.g., workout for 30 mins)
    programId?: string;     // For program specific tasks
    challengeId?: string;   // For challenge specific tasks
    metadata?: any;         // For any additional configuration
}

export interface CampaignTask {
    id?: string;
    type: TaskEventType;
    required: boolean;
    order: number;
    description: string;
    points: number;
    config?: TaskConfig;
}

export class ReferralCampaignEntity {
  id: string;
  slug?: string;
  name: string;
  description?: string;
  
  status: CampaignStatus;

    // Task Configuration
  tasks: CampaignTask[];
  isSequential?: boolean;       // Whether tasks must be completed in order
  completionDays?: number;
          // Days allowed to complete all tasks

    // Reward Configuration
  baseRewardConfig?: {
    referrerPoints: number;
    refereePoints: number;
  };
  maxRewardsPerDay?: number;   // Optional daily reward limit
  maxRewardsTotal?: number;    // Optional total reward limit
  rewardAmount?: number;    // Optional total reward amount limit

    // Campaign Settings
  validityDays: number;        // How long campaign is valid for
  startDate?: Date;            // Optional scheduled start
  endDate?: Date;             // Optional scheduled end
  maxReferralCodeUse?: number; // Optional limit per referral code
  singleRewardPerReferee?: boolean;
    // Media
  media?: ReferralCampaignMediaItem[];

    // Rules and Terms
  rules?: string[];           // Campaign rules
  terms?: string;            // Terms and conditions

    // Tracking
  totalRewardsIssued?: number;
  activeReferrals?: number;
  completedReferrals?: number;
  totalReferralCodes?: number;
  monthlyCapPerReferrer?: number;

    // Metadata
  isActive?: boolean;
  createdBy?: string;
  lastModifiedBy?: string;
}


