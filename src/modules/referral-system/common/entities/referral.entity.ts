
import { TaskStatus } from './referral-campaign.entity';

export enum ReferralStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
    EXPIRED = 'EXPIRED',
    FAILED = 'FAILED'
}

export interface ReferralTaskProgress {
    taskId: string;
    status: TaskStatus;
    progress?: { current: number; target: number };
    startedAt: Date;
    completedAt?: Date;
    lastUpdated: Date;
    metadata?: any;
}

export class ReferralEntity {
    id?: string;
    campaignId?: string;
    referralCode?: string;
    referrerId?: string;
    refereeId?: string;
    taskProgress?: ReferralTaskProgress[];
    isCompleted?: boolean;
    status?: ReferralStatus;
    createdAt?: Date;
    expiresAt?: Date;
    completedAt?: Date;
    rewardIssued?: boolean;
    pointsAwarded?: { referrer: number; referee: number };
}