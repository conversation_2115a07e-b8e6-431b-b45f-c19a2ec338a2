import { randomUUID } from "crypto";
import { model, Schema } from "mongoose";
import {
  CampaignStatus,
  CampaignTask,
  ReferralCampaignEntity,
  ReferralCampaignMediaItem,
  ReferralCampaignMediaItemType,
  TaskEventType
} from '../entities/referral-campaign.entity';

const ReferralCampaignMediaItemSchema = new Schema<ReferralCampaignMediaItem>(
  {
    type: {
      type: String,
      enum: Object.values(ReferralCampaignMediaItemType),
      required: true
    },
    url: {
      type: String,
      required: true
    }
  },
  { _id: false }
);

const TaskConfigSchema = new Schema({
  count: Number,
  duration: Number,
  programId: String,
  challengeId: String,
  metadata: { type: Schema.Types.Mixed, default: {} }
}, { _id: false });

const ReferralCampaignTaskSchema = new Schema<CampaignTask>({
  id: { type: String, default: () => randomUUID(), unique: true },
  type: { type: String, enum: Object.values(TaskEventType), required: true },
  required: { type: Boolean, default: true },
  order: { type: Number, default: 0 },
  config: { type: TaskConfigSchema, default: {} },
  description: { type: String, required: true },
  points: { type: Number, required: true }
}, { _id: false });

const ReferralCampaignSchema = new Schema<ReferralCampaignEntity>({
  id: { type: String, default: () => randomUUID(), unique: true },
  slug: { type: String, required: false, unique: true, lowercase: true, trim: true },
  name: { type: String, required: true },
  description: { type: String, required: false },
  status: { type: String, enum: Object.values(CampaignStatus), required: true, default: CampaignStatus.DRAFT },

  tasks: { type: [ReferralCampaignTaskSchema], default: [] },
  isSequential: { type: Boolean, default: false },
  completionDays: { type: Number, required: false },

  baseRewardConfig: {
    referrerPoints: { type: Number, required: true },
    refereePoints: { type: Number, required: true }
  },
  maxRewardsPerDay: { type: Number, default: null },
  maxRewardsTotal: { type: Number, default: null },
  rewardAmount: { type: Number, default: null },
  singleRewardPerReferee: { type: Boolean, default: true },

  validityDays: { type: Number, required: true },
  startDate: { type: Date, default: null },
  endDate: { type: Date, default: null },
  maxReferralCodeUse: { type: Number, default: null },

  totalRewardsIssued: { type: Number, default: 0 },
  activeReferrals: { type: Number, default: 0 },
  completedReferrals: { type: Number, default: 0 },
  totalReferralCodes: { type: Number, default: 0 },
  monthlyCapPerReferrer: { type: Number, default: 0 },
  

  media: {
    type: [ReferralCampaignMediaItemSchema],
    required: false,
    default: []
  },

  rules: { type: [String], default: [] },
  terms: { type: String, default: null },

  isActive: { type: Boolean, default: true },
  createdBy: { type: String, required: false },
  lastModifiedBy: { type: String, required: false }
}, {
  timestamps: true,
  versionKey: false
});

ReferralCampaignSchema.index({ slug: 1 }, { unique: true });

const ReferralCampaignModel = model<ReferralCampaignEntity>('referral_campaign_collection', ReferralCampaignSchema);

export { ReferralCampaignModel };

