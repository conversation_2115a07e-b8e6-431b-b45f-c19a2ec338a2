import { randomUUID } from 'crypto';
import { Schema, model } from 'mongoose';
import { ReferralCodeEntity, ReferralCodeStatus } from '../entities/referral-code.entity';

export const ReferralCodeSchema = new Schema({
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    code: { type: String, required: true, unique: true },
    userId: { type: String, required: true },
    campaignId: { type: String, required: true },
    
    // Usage Tracking
    usageCount: { type: Number, default: 0 },
    usageLimit: Number,
    lastUsedAt: Date,
    
    // Validity
    isActive: { type: Boolean, default: true },
    status: { 
        type: String, 
        enum: Object.values(ReferralCodeStatus),
        default: ReferralCodeStatus.ACTIVE
    },
    createdAt: { type: Date, default: Date.now },
    expiresAt: Date,
    
    // Stats
    totalPointsGenerated: { type: Number, default: 0 },
    successfulReferrals: { type: Number, default: 0 },
    pendingReferrals: { type: Number, default: 0 }
});

// Add indexes for better query performance
ReferralCodeSchema.index({ code: 1 }, { unique: true });
ReferralCodeSchema.index({ userId: 1, campaignId: 1 });
ReferralCodeSchema.index({ expiresAt: 1 });
ReferralCodeSchema.index({ status: 1 });

ReferralCodeSchema.index({ code: 1 }, { unique: true });

const ReferralCodeModel = model<ReferralCodeEntity>(
  'referral_code_collection',
  ReferralCodeSchema
);

export { ReferralCodeModel };

