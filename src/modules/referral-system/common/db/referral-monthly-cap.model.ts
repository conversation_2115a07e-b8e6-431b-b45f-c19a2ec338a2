import { randomUUID } from 'crypto';
import { Schema, model } from 'mongoose';
import { ReferralMonthlyCapEntity } from '../entities/referral-monthly-cap.entity';

export const ReferralMonthlyCapSchema = new Schema({
  id: {
    type: String,
    default: () => randomUUID(),
    unique: true,
  },
  userId: {
    type: String,
    required: true,
    index: true,
  },
  campaignId: {
    type: String,
    required: true,
    index: true,
  },
  month: {
    type: Number,
    required: true,
  },
  year: {
    type: Number,
    required: true,
  },
  pointsEarned: {
    type: Number,
    default: 0,
  },
  lastUpdated: {
    type: Date,
    default: Date.now,
  }
});

// Create a compound index for efficient lookups by user, campaign, month, and year
ReferralMonthlyCapSchema.index({ userId: 1, campaignId: 1, month: 1, year: 1 }, { unique: true });

export const ReferralMonthlyCapModel = model<ReferralMonthlyCapEntity>(
  'referral_monthly_cap_collection',
  ReferralMonthlyCapSchema
);
