import { randomUUID } from 'crypto';
import { Schema, model } from 'mongoose';
import { TaskStatus } from '../entities/referral-campaign.entity';
import { ReferralEntity, ReferralStatus } from '../entities/referral.entity';

const ReferralTaskProgressSchema = new Schema({
    taskId: String,
    status: { type: String, enum: Object.values(TaskStatus) },
    progress: {
        current: Number,
        target: Number
    },
    startedAt: Date,
    completedAt: Date,
    lastUpdated: Date,
    metadata: Schema.Types.Mixed
}, { _id: false });

export const ReferralSchema = new Schema({
    // id: { type: String, required: true, unique: true },
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
      index: true,
    },
    campaignId: { type: String, required: true },
    referralCode: { type: String, required: true },
    referrerId: { type: String, required: true },
    refereeId: { type: String, required: true },
    taskProgress: [ReferralTaskProgressSchema],
    isCompleted: { type: Boolean, default: false },
    status: { type: String, enum: Object.values(ReferralStatus), default: ReferralStatus.IN_PROGRESS },
    createdAt: { type: Date, default: Date.now },
    expiresAt: Date,
    completedAt: Date,
    rewardIssued: { type: Boolean, default: false },
    pointsAwarded: {
        referrer: Number,
        referee: Number
    }
});
ReferralSchema.index({ id: 1 });
ReferralSchema.index({ referralCode: 1 });
ReferralSchema.index({ refereeId: 1, campaignId: 1 });

const ReferralsModel = model<ReferralEntity>('referral_collection', ReferralSchema);

export { ReferralsModel };

