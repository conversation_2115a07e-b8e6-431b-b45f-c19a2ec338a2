// referral-participation.entity.ts

import { randomUUID } from 'crypto';
import { Schema, model } from 'mongoose';
import { ReferralParticipationEntity } from '../entities/referral-participation.entity';



// Mongoose schema definition
export const ReferralParticipationSchema = new Schema({
  id: {
    type: String,
    default: () => randomUUID(),
    unique: true,
  },
  userId: {
    type: String,
    required: true,
    index: true,
  },
  campaignId: {
    type: String,
    required: true,
    index: true,
  },
  referralCodeId: {
    type: String,
    required: true,
  },
  joinedAt: {
    type: Date,
    default: Date.now,
  },
});

// Indexes for efficient lookups
ReferralParticipationSchema.index({ userId: 1, campaignId: 1 }, { unique: true });

export const ReferralParticipationModel = model<ReferralParticipationEntity>(
  'referral_participation_collection',
  ReferralParticipationSchema
);
