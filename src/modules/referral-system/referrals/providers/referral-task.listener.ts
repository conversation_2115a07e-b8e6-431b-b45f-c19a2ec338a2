// src/modules/referral-system/event-listeners/referral-task.listener.ts

import { Injectable } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { IUserPointsSources, IUserPointsTransTypeEnum } from 'models';
import { UserPointsService } from 'src/modules/user-points/services';
import { CampaignProviderForUser } from '../../campaign/providers/user.provider';
import { TaskStatus } from '../../common/entities/referral-campaign.entity';
import { ReferralStatus } from '../../common/entities/referral.entity';
import { ReferralsCodeProviderForUser } from '../../referral-code/providers/user.provider';
import { ReferralMonthlyCapRepository } from '../repositories/referral-monthly-cap.repository';
import { ReferralsRepositoryForUser } from '../repositories/user.repository';

@Injectable()
export class ReferralTaskListener {
  constructor(
    private readonly referralsRepository: ReferralsRepositoryForUser,
    private readonly campaignProvider: CampaignProviderForUser,
    private readonly referralCodeProviderProvider: ReferralsCodeProviderForUser,
    private readonly pointsService: UserPointsService,
    private readonly monthlyCapRepository: ReferralMonthlyCapRepository,
    private eventEmitter: EventEmitter2,
  ) {}

  @OnEvent('program.subscription')
  async handleProgramSubscription(payload: {
    userId: string;
    programId: string;
    subscriptionDate: Date;
  }) {
    console.log('program subscription initiated');

    const { userId, programId, subscriptionDate } = payload;
    // console.log(userId,programId,)

    // 1. Find all active referrals for this user
    const activeReferrals =
      await this.referralsRepository.findActiveReferralsByUserId(userId);
    // console.log(activeReferrals)

    for (const referral of activeReferrals) {
      // 2. Get campaign details
      const campaign = await this.campaignProvider.getSingleCoachProgram(
        referral.campaignId,
      );
      referral.referralCode;
      // 3. Find the program subscription task in this campaign
      const programTask = campaign.tasks.find(
        (task) => task.type === 'SUBSCRIBE_TO_PROGRAM',
      );

      // console.log('program task', programTask)

      if (!programTask) continue; // Skip if campaign has no program subscription task

      // 4. Find the corresponding task in the referral's progress
      const taskProgress = referral.taskProgress.find(
        (tp) => tp.taskId === programTask.id,
      );

      // console.log('task progress',taskProgress )

      if (!taskProgress || taskProgress.status === TaskStatus.COMPLETED) {
        continue; // Skip if task not found or already completed
      }

      // 5. Check if the program subscription meets campaign requirements
      if (this.doesProgramMeetRequirements(programId, programTask, campaign)) {
        // 6. Update task status
        taskProgress.status = TaskStatus.COMPLETED;
        taskProgress.completedAt = new Date();
        taskProgress.lastUpdated = new Date();
        taskProgress.metadata = {
          ...taskProgress.metadata,
          programId,
          subscriptionDate,
        };

        // 7. Update referral status
        const allTasksCompleted = this.checkAllTasksCompleted(
          referral,
          campaign,
        );

        // console.log('all task completed',allTasksCompleted)

        if (allTasksCompleted) {
          referral.status = ReferralStatus.COMPLETED;
          referral.isCompleted = true;
          referral.completedAt = new Date();
          referral.rewardIssued = true;
        }

        // console.log('referral ',referral)

        // 8. Save the updated referral
        await this.referralsRepository.updateReferral(referral);

        // 9. If all tasks are completed, emit completion event
        if (allTasksCompleted) {
          // console.log()
          this.eventEmitter.emit('referral.completed', {
            referralId: referral.id,
            campaignId: referral.campaignId,
            referrerId: referral.referrerId,
            refereeId: referral.refereeId,
            completedAt: referral.completedAt,
          });

          // Check monthly cap for referrer before awarding points
          const referrerPoints = campaign.baseRewardConfig.referrerPoints;
          const refereePoints = campaign.baseRewardConfig.refereePoints;
          const monthlyCap = campaign.monthlyCapPerReferrer || 0;

          // If no monthly cap is set (0 or null), award points directly
          if (monthlyCap <= 0) {
            await this.pointsService.updateReferrerAndReferralPoints(
              referral.referrerId,
              referral.refereeId,
              referrerPoints,
              refereePoints,
              IUserPointsSources.Referrals,
              referral.id,
              campaign.name,
            );

            // Store the points awarded in the referral record
            referral.pointsAwarded = {
              referrer: referrerPoints,
              referee: refereePoints,
            };

            await this.referralsRepository.updateReferral(referral);
          } else {
            // Check if adding these points would exceed the monthly cap
            const canAwardPoints =
              await this.monthlyCapRepository.addPointsIfUnderCap(
                referral.referrerId,
                referral.campaignId,
                referrerPoints,
                monthlyCap,
              );

            if (canAwardPoints) {
              // Award points to both referrer and referee
              await this.pointsService.updateReferrerAndReferralPoints(
                referral.referrerId,
                referral.refereeId,
                referrerPoints,
                refereePoints,
                IUserPointsSources.Referrals,
                referral.id,
                campaign.name,
              );

              // Store the points awarded in the referral record
              referral.pointsAwarded = {
                referrer: referrerPoints,
                referee: refereePoints,
              };
            } else {
              // If cap would be exceeded, only award points to the referee
              await this.pointsService.updatePoints(referral.refereeId, {
                type: IUserPointsTransTypeEnum.EARNED,
                pointSourceType: IUserPointsSources.Referrals,
                pointSourceId: referral.id,
                pointSourceName: campaign.name,
                points: refereePoints,
              });

              // Store the points awarded in the referral record
              referral.pointsAwarded = {
                referrer: 0, // No points awarded due to monthly cap
                referee: refereePoints,
              };

              console.log(
                `Monthly cap reached for referrer ${referral.referrerId} in campaign ${referral.campaignId}`,
              );
            }

            await this.referralsRepository.updateReferral(referral);
          }

          // Increment the usage count for the referral code
          await this.referralCodeProviderProvider.incrementUsage(
            referral.referralCode,
          );
        }
      }
    }
  }

  // @OnEvent('user.onboarding.completed')
  // async handleOnboardingCompleted(payload: { userId: string }) {
  //   console.log('onboarding completed event received');

  //   const { userId } = payload;

  //   // 1. Find all active referrals for this user
  //   const activeReferrals = await this.referralsRepository.findActiveReferralsByUserId(userId);

  //   for (const referral of activeReferrals) {
  //     // 2. Get campaign details
  //     const campaign = await this.campaignProvider.getSingleCoachProgram(referral.campaignId);

  //     if (!campaign || !campaign.tasks) {
  //       console.warn(`Campaign not found or has no tasks for campaignId: ${referral.campaignId}`);
  //       continue;
  //     }

  //     // 3. Find the onboarding task in this campaign
  //     const onboardingTask = campaign.tasks.find(
  //       task => task.type === 'COMPLETE_ONBOARDING'
  //     );

  //     if (!onboardingTask) continue; // Skip if no onboarding task

  //     // 4. Find the corresponding task in the referral's progress
  //     const taskProgress = referral.taskProgress.find(
  //       tp => tp.taskId === onboardingTask.id
  //     );

  //     if (!taskProgress || taskProgress.status === TaskStatus.COMPLETED) {
  //       continue; // Skip if task not found or already completed
  //     }

  //     // 5. Mark task as completed
  //     taskProgress.status = TaskStatus.COMPLETED;
  //     taskProgress.completedAt = new Date();
  //     taskProgress.lastUpdated = new Date();

  //     // 6. Update referral status if all tasks are completed
  //     const allTasksCompleted = this.checkAllTasksCompleted(referral, campaign);

  //     if (allTasksCompleted) {
  //       referral.status = ReferralStatus.COMPLETED;
  //       referral.isCompleted = true;
  //       referral.completedAt = new Date();
  //     }

  //     // 7. Save the updated referral
  //     await this.referralsRepository.updateReferral(referral);

  //     // Check monthly cap for referrer before awarding points
  //     const referrerPoints = campaign.baseRewardConfig.referrerPoints;
  //     const refereePoints = campaign.baseRewardConfig.refereePoints;
  //     const monthlyCap = campaign.monthlyCapPerReferrer || 0;

  //     // If no monthly cap is set (0 or null), award points directly
  //     if (monthlyCap <= 0) {
  //       await this.pointsService.updateReferrerAndReferralPoints(
  //         referral.referrerId,
  //         referral.refereeId,
  //         referrerPoints,
  //         refereePoints,
  //         IUserPointsSources.Referrals,
  //         referral.id,
  //         campaign.name
  //       );

  //       // Store the points awarded in the referral record
  //       referral.pointsAwarded = {
  //         referrer: referrerPoints,
  //         referee: refereePoints
  //       };

  //       await this.referralsRepository.updateReferral(referral);
  //     } else {
  //       // Check if adding these points would exceed the monthly cap
  //       const canAwardPoints = await this.monthlyCapRepository.addPointsIfUnderCap(
  //         referral.referrerId,
  //         referral.campaignId,
  //         referrerPoints,
  //         monthlyCap
  //       );

  //       if (canAwardPoints) {
  //         // Award points to both referrer and referee
  //         await this.pointsService.updateReferrerAndReferralPoints(
  //           referral.referrerId,
  //           referral.refereeId,
  //           referrerPoints,
  //           refereePoints,
  //           IUserPointsSources.Referrals,
  //           referral.id,
  //           campaign.name
  //         );

  //         // Store the points awarded in the referral record
  //         referral.pointsAwarded = {
  //           referrer: referrerPoints,
  //           referee: refereePoints
  //         };
  //       } else {
  //         // If cap would be exceeded, only award points to the referee
  //         await this.pointsService.updatePoints(referral.refereeId, {
  //           type: IUserPointsTransTypeEnum.EARNED,
  //           pointSourceType: IUserPointsSources.Referrals,
  //           pointSourceId: referral.id,
  //           pointSourceName: campaign.name,
  //           points: refereePoints
  //         });

  //         // Store the points awarded in the referral record
  //         referral.pointsAwarded = {
  //           referrer: 0, // No points awarded due to monthly cap
  //           referee: refereePoints
  //         };

  //         console.log(`Monthly cap reached for referrer ${referral.referrerId} in campaign ${referral.campaignId}`);
  //       }

  //       await this.referralsRepository.updateReferral(referral);
  //     }

  //     // 8. If all tasks completed, emit referral completed event
  //     if (allTasksCompleted) {
  //       this.eventEmitter.emit('referral.completed', {
  //         referralId: referral.id,
  //         campaignId: referral.campaignId,
  //         referrerId: referral.referrerId,
  //         refereeId: referral.refereeId,
  //         completedAt: referral.completedAt
  //       });
  //     }
  //   }
  // }

  private doesProgramMeetRequirements(
    programId: string,
    task: any,
    campaign: any,
  ): boolean {
    // Check if the program meets the task requirements
    // For example, check if it's the correct program type, etc.
    // This is a simplified version - adjust based on your requirements
    return true;
  }

  private checkAllTasksCompleted(referral: any, campaign: any): boolean {
    // Check if all required tasks are completed
    const requiredTaskIds = campaign.tasks
      .filter((task) => task.required)
      .map((task) => task.id);

    const completedTaskIds = referral.taskProgress
      .filter((task) => task.status === 'COMPLETED')
      .map((task) => task.taskId);

    return requiredTaskIds.every((id) => completedTaskIds.includes(id));
  }
}
