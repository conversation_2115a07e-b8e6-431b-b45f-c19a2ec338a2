import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { UserInfoProvider } from 'src/modules/user/providers/user.provider';
import { CampaignProviderForUser } from '../../campaign/providers/user.provider';

import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  throwBadReqErrIf,
  throwNotFoundErr,
} from 'src/internal/exception/api.exception.ext';
import {
  CAMPAIGN_NOT_FOUND,
  DUPLICATE_REFERRAL_USE,
  REFERRAL_CODE_EXPIRED,
  REFERRER_CODE_NOT_FOUND,
  SELF_REFERRAL_NOT_ALLOWED,
  USAGE_LIMIT_REACHED,
} from '../../common/const/referral.const';
import { TaskStatus } from '../../common/entities/referral-campaign.entity';
import {
  ReferralEntity,
  ReferralStatus,
} from '../../common/entities/referral.entity';
import { ReferralsCodeProviderForUser } from '../../referral-code/providers/user.provider';
import {
  ReferralResponseDto,
  ReferralSuccessResponseDto,
} from '../dtos/create-referrals.dto';
import { ReferralParticipationRepository } from '../repositories/referral-participation.repository';
import { ReferralsRepositoryForUser } from '../repositories/user.repository';

@Injectable()
export class ReferralsServiceForUser {
  constructor(
    private readonly referralsRepository: ReferralsRepositoryForUser,
    @Inject(forwardRef(() => UserInfoProvider))
    private readonly userInfoProvider: UserInfoProvider,
    @Inject(forwardRef(() => CampaignProviderForUser))
    private readonly campaignProvider: CampaignProviderForUser,
    @Inject(forwardRef(() => ReferralsCodeProviderForUser))
    private readonly referralCodeProvider: ReferralsCodeProviderForUser,
    private readonly helper: Helper,
    private readonly eventEmitter: EventEmitter2,
    private readonly referralParticipationRepo: ReferralParticipationRepository,
  ) {}

  /**
   * 🧠 Referral Code Validation – Step-by-step
   *
   * 1. Check if the user exists
   *    - Use `refereeUserId` to fetch user.
   *    - If not found → throw error: "User Not Found!"
   *
   * 2.  Check if referral code exists
   *    - Look up referral code from DB.
   *    - If not found → throw error: "Referrals Not Found!"
   *
   * 3. Check if the campaign exists and is active
   *    - Use `campaignId` from referralCode.
   *    - Validate campaign is not deleted/inactive.
   *    - If invalid → throw error: "Campaign Not Found!"
   *
   * 4. Prevent self-referral
   *    - If `refereeUserId === referralCodeInfo.userId`
   *    - Throw error: "You cannot use your own referral code."
   *
   * 5. Prevent multiple uses for the same campaign
   *    - Use helper `hasUserAlreadyUsedCampaign(refereeUserId, campaignId)`
   *    - If true → throw error: "You have already used a referral code for this campaign."
   *
   * 6. (Optional) Check referral code expiry
   *    - If `referralCodeInfo.expiresAt < now`
   *    - Throw: "This referral code has expired."
   *
   * 7.  (Optional) Check usage limit
   *    - If `usageLimit` set and `usageCount >= usageLimit`
   *    - Throw: "This referral code has reached its usage limit."
   */
  async validateAndUseReferralCode(dto: {
    referralCode: string;
    refereeUserId: string;
  }): Promise<ReferralSuccessResponseDto> {
    const { referralCode, refereeUserId } = dto;

    // 1. Check if referral code exists and is active
    const code = await this.referralCodeProvider.getSingleReferralCode(
      referralCode,
    );
    if (!code || !code.isActive || code.status !== 'ACTIVE') {
      throwNotFoundErr(
        true,
        'Referral Code id not found!',
        REFERRER_CODE_NOT_FOUND,
      );
    }

    // Get existing participation
    const existingParticipation =
      await this.referralParticipationRepo.hasUserJoinedCampaign(
        refereeUserId,
        code.campaignId,
      );

    // If there's existing participation, check the referral status
    if (existingParticipation) {
      // Find the existing referral with this participation
      const existingReferral =
        await this.referralsRepository.findReferralByUserAndCampaign(
          refereeUserId,
          code.campaignId,
        );

      // Only throw the duplicate error if referral is COMPLETED
      if (
        existingReferral &&
        existingReferral.status === ReferralStatus.COMPLETED
      ) {
        throwBadReqErrIf(
          true,
          'You have already used this referral code',
          DUPLICATE_REFERRAL_USE,
        );
      }

      // If referral exists but isn't completed, check if they're trying to use a different referrer's code
      if (
        existingReferral &&
        existingReferral.status === ReferralStatus.IN_PROGRESS
      ) {
        // Check if the new referral code belongs to a different referrer
        if (existingReferral.referralCode !== referralCode) {
          throwBadReqErrIf(
            true,
            `You've already joined this campaign using referral code "${existingReferral.referralCode}". You cannot use a different referrer's code for the same campaign.`,
            'DIFFERENT_REFERRER_NOT_ALLOWED',
          );
        }

        // If it's the same referral code, return the existing referral
        const campaign = await this.campaignProvider.getSingleCoachProgram(
          existingReferral.campaignId,
        );

        const responseData: ReferralResponseDto = {
          id: existingReferral.id,
          referralCode: existingReferral.referralCode,
          referrerId: existingReferral.referrerId,
          refereeId: existingReferral.refereeId,
          campaignId: existingReferral.campaignId,
          campaignName: campaign.name,
          status: existingReferral.status,
          rewardIssued: existingReferral.rewardIssued,
          isCompleted: existingReferral.isCompleted,
        };

        return this.helper.serviceResponse.successResponse(responseData);
      }
    }

    // 3. Check if the campaign exists and is active
    const campaign = await this.campaignProvider.getSingleCoachProgram(
      code.campaignId,
    );
    if (!campaign || !campaign.isActive || campaign.status !== 'ACTIVE') {
      throwNotFoundErr(
        !campaign,
        'Referral Campaign not found!',
        CAMPAIGN_NOT_FOUND,
      );
    }

    // 4. Prevent self-referral
    if (code.userId === refereeUserId) {
      throwBadReqErrIf(
        code.userId === refereeUserId,
        'Self Referral Not Allowed',
        SELF_REFERRAL_NOT_ALLOWED,
      );
    }

    if (code.expiresAt && code.expiresAt < new Date()) {
      throwBadReqErrIf(
        code.expiresAt && code.expiresAt < new Date(),
        'This referral code has expired.',
        REFERRAL_CODE_EXPIRED,
      );
    }

    if (code.usageLimit && code.usageCount >= code.usageLimit) {
      throwBadReqErrIf(
        code.usageLimit && code.usageCount >= code.usageLimit,
        'This referral code has reached its usage limit.',
        USAGE_LIMIT_REACHED,
      );
    }

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + campaign.completionDays);

    const taskProgress = campaign.tasks.map((task) => ({
      taskId: task.id,
      status: TaskStatus.PENDING,
      startedAt: new Date(),
      lastUpdated: new Date(),
    }));

    const referral: Partial<ReferralEntity> = {
      campaignId: code.campaignId,
      referralCode: code.code,
      referrerId: code.userId,
      // refereeId: newUserId,
      refereeId: refereeUserId,
      taskProgress,
      isCompleted: false,
      status: ReferralStatus.IN_PROGRESS,
      expiresAt,
      rewardIssued: false,
    };

    const createdReferral = await this.referralsRepository.createReferrals(
      referral,
    );

    await this.referralParticipationRepo.createParticipation(
      refereeUserId,
      code.campaignId,
      code.id,
    );

    // 6. Update code stats
    // await this.referralCodeRepository.incrementUsage(code.id);
    await this.referralCodeProvider.incrementPendingReferrals(code.id);

    // 7. Emit event
    this.eventEmitter.emit('referral.created', {
      referralId: createdReferral.id,
      campaignId: createdReferral.campaignId,
      referrerId: createdReferral.referrerId,
      refereeId: createdReferral.refereeId,
    });

    const responseData: ReferralResponseDto = {
      id: createdReferral.id,
      referralCode: createdReferral.referralCode,
      referrerId: createdReferral.referrerId,
      refereeId: createdReferral.refereeId,
      campaignId: createdReferral.campaignId,
      campaignName: campaign.name,
      status: createdReferral.status,
      rewardIssued: createdReferral.rewardIssued,
      isCompleted: createdReferral.isCompleted,
    };

    return this.helper.serviceResponse.successResponse(responseData);
  }
}
