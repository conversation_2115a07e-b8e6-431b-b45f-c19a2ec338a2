

import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
// import { ReferralCampaignType } from '../../common/entities/referral-campaign.entity';
// import { ReferralCompletionStatusEnum } from '../../common/entities/referral.entity';


export class UseReferralCodeRequestDto {
  @ApiProperty({ example: 'FM124CAGFG', description: 'Referral code provided by the referrer' })
  @IsString()
  @IsNotEmpty()
  referralCode: string;

  @ApiProperty({ example: 'user456', description: 'User ID of the person using the referral code' })
  @IsString()
  @IsNotEmpty()
  refereeUserId: string;
}


export class ReferralResponseDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  referralCode: string;

  @Expose()
  @ApiProperty()
  referrerId: string;

  @Expose()
  @ApiProperty()
  refereeId: string;

  @Expose()
  @ApiProperty()
  campaignId: string;

  @Expose()
  @ApiProperty()
  campaignName: string;

  @Expose()
  @ApiProperty()
  status: string;

  @Expose()
  @ApiProperty({ required: false })
  rewardIssued?: boolean; 

  @Expose()
  @ApiProperty({ required: false })
  isCompleted?: boolean;
}


export class ReferralSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ type: ReferralResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: ReferralResponseDto;
}