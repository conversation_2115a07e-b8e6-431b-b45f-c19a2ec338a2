import { Injectable } from '@nestjs/common';
import { ReferralsModel } from '../../common/db/referrals.model';
import { ReferralEntity } from '../../common/entities/referral.entity';

@Injectable()
export class ReferralsRepositoryForUser {
  async createReferrals(info: ReferralEntity): Promise<ReferralEntity> {
    const referralCode = new ReferralsModel(info);
    const referralCodeDocument = await referralCode.save();
    return referralCodeDocument.toObject();
  }
  async hasUserAlreadyUsedCampaign(
    refereeUserId: string,
    campaignId: string,
  ): Promise<boolean> {
    const existingReferral = await ReferralsModel.findOne({
      refereeId: refereeUserId,
      campaignId: campaignId,
    }).lean();

    return !!existingReferral;
  }

  async findActiveReferralsByUserId(userId: string): Promise<ReferralEntity[]> {
    return await ReferralsModel.find({
      refereeId: userId,
      isCompleted: false,
      status: 'IN_PROGRESS',
      expiresAt: { $gt: new Date() },
    }).exec();
  }

  async updateReferral(referral: ReferralEntity): Promise<ReferralEntity> {
    return await ReferralsModel.findOneAndUpdate(
      { id: referral.id },
      { $set: referral },
      { new: true },
    ).exec();
  }

  async findReferralByUserAndCampaign(
    refereeUserId: string,
    campaignId: string,
  ): Promise<ReferralEntity | null> {
    const existingReferral = await ReferralsModel.findOne({
      refereeId: refereeUserId,
      campaignId: campaignId,
    }).lean();

    return existingReferral;
  }
}
