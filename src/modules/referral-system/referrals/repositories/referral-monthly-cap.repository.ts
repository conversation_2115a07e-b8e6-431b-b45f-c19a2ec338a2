import { Injectable } from '@nestjs/common';
import { ReferralMonthlyCapModel } from '../../common/db/referral-monthly-cap.model';

@Injectable()
export class ReferralMonthlyCapRepository {
  /**
   * Get the current month's points earned by a referrer for a specific campaign
   */
  async getCurrentMonthPoints(userId: string, campaignId: string): Promise<number> {
    const now = new Date();
    const month = now.getMonth() + 1; // JavaScript months are 0-indexed
    const year = now.getFullYear();
    
    const record = await ReferralMonthlyCapModel.findOne({
      userId,
      campaignId,
      month,
      year
    }).lean();
    
    return record ? record.pointsEarned : 0;
  }
  
  /**
   * Update the points earned by a referrer for a campaign in the current month
   * Returns true if the points were added successfully, false if it would exceed the monthly cap
   */
  async addPointsIfUnderCap(
    userId: string, 
    campaignId: string, 
    pointsToAdd: number, 
    monthlyCap: number
  ): Promise<boolean> {
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();
    
    // Find or create the monthly record
    let record = await ReferralMonthlyCapModel.findOne({
      userId,
      campaignId,
      month,
      year
    });
    
    // If no record exists, create a new one
    if (!record) {
      record = new ReferralMonthlyCapModel({
        userId,
        campaignId,
        month,
        year,
        pointsEarned: 0,
        lastUpdated: now
      });
    }
    
    // Check if adding these points would exceed the monthly cap
    if (record.pointsEarned + pointsToAdd > monthlyCap) {
      return false; // Would exceed cap
    }
    
    // Update the record with the new points
    record.pointsEarned += pointsToAdd;
    record.lastUpdated = now;
    await record.save();
    
    return true; // Points added successfully
  }
  
  /**
   * Get a summary of monthly points earned by a referrer across all campaigns
   */
  async getMonthlySummary(userId: string): Promise<any[]> {
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();
    
    return ReferralMonthlyCapModel.find({
      userId,
      month,
      year
    }).lean();
  }
}
