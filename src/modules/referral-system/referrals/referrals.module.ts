import { forwardRef, Module } from "@nestjs/common";
import { UserPointsModule } from "src/modules/user-points/user-points.module";
import { UserModule } from "src/modules/user/user.rest.module";
import { CampaignModule } from "../campaign/campaign.module";
import { ReferralCodeModule } from "../referral-code/referral-code.module";
import { ReferralsControllerForUser } from "./controllers/user.controller";
import { ReferralTaskListener } from "./providers/referral-task.listener";
import { ReferralMonthlyCapRepository } from "./repositories/referral-monthly-cap.repository";
import { ReferralParticipationRepository } from "./repositories/referral-participation.repository";
import { ReferralsRepositoryForUser } from "./repositories/user.repository";
import { ReferralsServiceForUser } from "./services/user.service";


@Module({
    controllers: [
        ReferralsControllerForUser
      ],

    imports: [
      forwardRef(() => UserModule),
      forwardRef(() => CampaignModule),
      forwardRef(() => ReferralCodeModule),
      UserPointsModule
    ],
  providers: [
    ReferralsServiceForUser,
    ReferralsRepositoryForUser,
    ReferralTaskListener,
    ReferralParticipationRepository,
    ReferralMonthlyCapRepository
  ],
    exports: [ReferralsRepositoryForUser],
    
})
export class ReferralsModule{ }