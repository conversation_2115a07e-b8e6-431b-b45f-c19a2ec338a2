import { Module } from "@nestjs/common";
import { UserPointsModule } from "../user-points/user-points.module";
import { CampaignModule } from "./campaign/campaign.module";
import { ReferralCodeModule } from "./referral-code/referral-code.module";
import { ReferralsModule } from "./referrals/referrals.module";

@Module({
    imports: [CampaignModule, ReferralCodeModule, ReferralsModule,UserPointsModule],
    // providers:[ReferralTaskListener]
    // exports:[CampaignModule, ReferralCodeModule]
})


export class ReferralSystemModule{ }