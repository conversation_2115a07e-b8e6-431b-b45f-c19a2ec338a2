import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';

export class GetReferralCodeDto {
    @ApiProperty({ description: 'User ID requesting the referral code' })
    @IsString()
    @IsNotEmpty()
    userId: string;

    @ApiProperty({ description: 'Campaign ID for which to generate a code' })
    @IsString()
    @IsNotEmpty()
    campaignId: string;
}

// export class ReferralCodeResponseDto {
//   @Expose()
//   @ApiProperty()
//   id: string;

//   @Expose()
//   @ApiProperty()
//   code: string;

//   @Expose()
//   @ApiProperty()
//   userId: string;

//   @Expose()
//   @ApiProperty()
//   campaignId: string;

//   @Expose()
//   @ApiProperty({ required: false })
//   isActive?: boolean;

//   @Expose()
//   @ApiProperty({ required: false })
//   usageLimit?: number;

//   @Expose()
//   @ApiProperty({ required: false })
//   usageCount?: number;

//   @Expose()
//   @ApiProperty({ required: false, type: String, format: 'date-time' })
//   @Type(() => Date)
//   expiresAt?: Date;

//   @Expose()
//   @ApiProperty({ type: String, format: 'date-time' })
//   @Type(() => Date)
//   createdAt: Date;

//   @Expose()
//   @ApiProperty({ type: String, format: 'date-time' })
//   @Type(() => Date)
//   updatedAt: Date;
// }
export class ReferralCodeResponseDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  code: string;

  @Expose()
  @ApiProperty()
  campaignId: string;

  @Expose()
  @ApiProperty()
  campaignName: string;

  @Expose()
  @ApiProperty()
  expiresAt: Date;

  @Expose()
  @ApiProperty()
  referrerPoints: number;

  @Expose()
  @ApiProperty()
  refereePoints: number;
}


export class ReferralCodeSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ type: ReferralCodeResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: ReferralCodeResponseDto;
}