import {
    Body,
    Controller,
    HttpStatus,
    Post,
    UseGuards
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiBody,
    ApiOperation,
    ApiResponse,
    ApiTags
} from '@nestjs/swagger';
import {
    OnlyAuthGuard,
    OnlyRoleGuard,
    Role,
    Roles,
} from 'src/authentication/guards/auth-role.guard';
import { REFERRAL_API_FOR_USER } from '../../common/const/swagger.const';
import { GetReferralCodeDto, ReferralCodeSuccessResponseDto } from '../dtos/create-referral-code.dto';
import { ReferralCodeServiceForUser } from '../services/user.service';

@ApiTags(REFERRAL_API_FOR_USER)
@ApiBearerAuth()
@Controller('user/referral')
export class ReferralCampaignControllerForUser { 
    constructor(private service: ReferralCodeServiceForUser) { }
    
    @Post('referral-codes')
    @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
    @Roles(Role.User)
    @ApiOperation({ summary: 'Get Referral code for a campaign' })
    @ApiBody({ type: GetReferralCodeDto })
    @ApiResponse({
        description: 'Returns the newly created Referral code Response',
        type: ReferralCodeSuccessResponseDto,
        status: HttpStatus.CREATED,
    })
    async createReferralCampaign(@Body() reqDto: GetReferralCodeDto) {
        return await this.service.getReferralCode(reqDto);
     }
}