import { Injectable } from "@nestjs/common";
import { ReferralCodeModel } from "../../common/db/referral-code.model";
import { ReferralCodeEntity } from "../../common/entities/referral-code.entity";


@Injectable()
export class ReferralCodeRepositoryForUser {
      async createReferralCode(info: ReferralCodeEntity): Promise<ReferralCodeEntity> {
        const referralCode = new ReferralCodeModel(info);
        const referralCodeDocument = await referralCode.save();
        return referralCodeDocument.toObject();
      }
  

      async getLatestReferralCodeByUserAndCampaign(userId: string, campaignId: string): Promise<ReferralCodeEntity | null> {
        const query = { userId, campaignId };
        const doc = await ReferralCodeModel.findOne(query)
          .sort({ createdAt: -1 }) // sort newest first
          .exec();
        return doc ? doc.toObject() : null;
       }
  
      async findByCode(code: string): Promise<ReferralCodeEntity | null> {
        const referralCodeDoc = await ReferralCodeModel.findOne({ code: code.toUpperCase() }).exec();
        return referralCodeDoc ? referralCodeDoc.toObject() : null;
      }
  
      async incrementUsage(code: string): Promise<void> {
        await ReferralCodeModel.updateOne(
          { code },
          {
            $inc: {
              usageCount: 1,
              successfulReferrals: 1,
              pendingReferrals: -1
            },
            $set: {
              lastUsedAt: new Date()
            }
          }
        ).exec();
      }
      
      
      
      async incrementPendingReferrals(id: string): Promise<void> {
        await ReferralCodeModel.updateOne(
          { id },
          { $inc: { pendingReferrals: 1 } }
        ).exec();
      }
      async incrementSuccessFulReferrals(id: string): Promise<void> {
        await ReferralCodeModel.updateOne(
          { id },
          { $inc: { successfulReferrals: 1 } }
        ).exec();
      }
  


}