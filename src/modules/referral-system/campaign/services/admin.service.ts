import { Injectable } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { Helper } from "src/helper/helper.interface";
import { deepCasting, shallowCasting } from "src/internal/casting/object.casting";
import { throwNotFoundErr } from "src/internal/exception/api.exception.ext";
import { CAMPAIGN_NOT_FOUND, DUPLICATE_TASK_EXIST, INVALID_POINT, REFERRAL_INFO_NOT_FOUND, TASK_SEQUENTIAL_EXIST, TASK_SHOULD_EXIST } from "../../common/const/referral.const";
import { CampaignStatus, ReferralCampaignEntity } from "../../common/entities/referral-campaign.entity";
import { CampaignResponseDto, CreateCampaignDto, ReferralCampaignSuccessResponseDto } from "../dtos/create-referral-campaign.dto";

import { CampaignStatusForAdmin, getMultipleReferralCampaignSuccessResponseDto, GetReferralCampaignResponseDto, getSingleReferralCampaignSuccessResponseDto } from "../dtos/get-referral-campaign.dto";
import { UpdateReferralCampaignRequestDto } from "../dtos/update-referral-campaign.dto";
import { ReferralCampaignRepositoryForAdmin } from "../repositories/admin.repository";

@Injectable()

export class ReferralCampaignServiceForAdmin{
    constructor(
    private readonly campaignRepository: ReferralCampaignRepositoryForAdmin,
    private readonly helper: Helper,
    private readonly eventEmitter: EventEmitter2
    ) { }
    
    async createReferralCampaign(dto: CreateCampaignDto): Promise<ReferralCampaignSuccessResponseDto> {
        this.validateCampaignData(dto);

        const campaignEntity = shallowCasting(ReferralCampaignEntity, {
            ...dto,
            status: CampaignStatus.ACTIVE,
            isActive: true,
            totalRewardsIssued: 0,
            activeReferrals: 0,
            completedReferrals: 0,
            totalReferralCodes: 0,
            createdBy: 'admin', //  todo user._id, or user.email, based on what you store
        });
        
        // Save to repository
        const createdCampaign = await this.campaignRepository.createCampaign(campaignEntity);

         this.eventEmitter.emit('campaign.created', {
            campaignId: createdCampaign.id,
            name: createdCampaign.name
         });
        const responseDto = deepCasting(CampaignResponseDto, createdCampaign);
        return this.helper.serviceResponse.successResponse(responseDto);
    }
    

    private validateCampaignData(dto: CreateCampaignDto): void {
        // Check if tasks are valid
        if (!dto.tasks || dto.tasks.length === 0) {
             throwNotFoundErr(
            !dto.tasks,
            'Task should exist!',
            TASK_SHOULD_EXIST,
            );
        }
        
        // Check for duplicate task types
        const taskTypes = dto.tasks.map(task => task.type);
        const uniqueTaskTypes = new Set(taskTypes);
        if (taskTypes.length !== uniqueTaskTypes.size) {
            throwNotFoundErr(
            !uniqueTaskTypes.size,
            'Duplicate task types are not allowed',
            DUPLICATE_TASK_EXIST,
            );
        }
        
        // Check task order if sequential
        if (dto.isSequential) {
            const taskOrders = dto.tasks.map(task => task.order);
            const sortedOrders = [...taskOrders].sort((a, b) => a - b);
            const isSequential = taskOrders.every((order, index) => order === sortedOrders[index]);
            
            if (!isSequential) {
                // throw new BadRequestException('Task orders must be sequential and unique');
                throwNotFoundErr(
                !isSequential,
                'Duplicate task types are not allowed',
                TASK_SEQUENTIAL_EXIST,
                );
                
            }
        }
        
        // Validate reward configuration
        if (dto.baseRewardConfig.referrerPoints <= 0 || dto.baseRewardConfig.refereePoints <= 0) {
            // throw new BadRequestException('Reward points must be greater than zero');
             throwNotFoundErr(
                dto.baseRewardConfig.referrerPoints <= 0 || dto.baseRewardConfig.refereePoints <= 0,
                'Reward points must be greater than zero',
                INVALID_POINT,
                );
        }
        
        // Validate dates
        // if (dto.startDate && dto.endDate && new Date(dto.startDate) >= new Date(dto.endDate)) {
        //     // throw new BadRequestException('End date must be after start date');
        // }
    }
    
   

    async getMultipleReferralCampaign(adminCampaignStatus: CampaignStatusForAdmin, offset: number, limit: number): Promise<getMultipleReferralCampaignSuccessResponseDto> {
        const newDocs = await this.campaignRepository.getReferralCampaigns(adminCampaignStatus, offset,limit);
        const responseDto = newDocs.map(item => deepCasting(GetReferralCampaignResponseDto, item));
        return this.helper.serviceResponse.successResponse(responseDto);
    }

    async getSingleReferralCampaign(campaignId: String): Promise<getSingleReferralCampaignSuccessResponseDto> {
        const referralDoc = await this.campaignRepository.getReferralCampaign(campaignId);
        throwNotFoundErr(!referralDoc, "Referral Campaign not found!", CAMPAIGN_NOT_FOUND);
    
        const responseDto = deepCasting(GetReferralCampaignResponseDto, referralDoc);
        return this.helper.serviceResponse.successResponse(responseDto);
    }

    async updateSingleReferralCampaign(campaignId: string, updateDto: UpdateReferralCampaignRequestDto): Promise<ReferralCampaignSuccessResponseDto> {
        const referralInfo = shallowCasting(ReferralCampaignEntity, updateDto);
        const newObj = await this.campaignRepository.updateReferralCampaign(campaignId, referralInfo);
        throwNotFoundErr(!newObj, "Referral Campaign not found!", REFERRAL_INFO_NOT_FOUND);
    
        const responseDto = deepCasting(GetReferralCampaignResponseDto, newObj);
        return this.helper.serviceResponse.successResponse(responseDto);
      }
    
    
}