import {
    <PERSON>,
    Get,
    HttpStatus,
    Param,
    ParseU<PERSON><PERSON><PERSON><PERSON>,
    UseGuards,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiOperation,
    ApiParam,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import {
    OnlyAuthGuard,
    OnlyRoleGuard,
    Role,
    Roles,
} from 'src/authentication/guards/auth-role.guard';
import { REFERRAL_API_FOR_USER } from '../../common/const/swagger.const';
import {
    getMultipleReferralCampaignSuccessResponseDto,
    getSingleReferralCampaignSuccessResponseDto,
} from '../dtos/get-referral-campaign.dto';
import { ReferralCampaignServiceForUser } from '../services/user.service';

@ApiTags(REFERRAL_API_FOR_USER)
@ApiBearerAuth()
@Controller('user/referral')
export class ReferralCampaignControllerForUser {
  constructor(private service: ReferralCampaignServiceForUser) {}

  @Get('campaigns')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'Get multiple campaigns' })
  @ApiResponse({
    description: 'Get multiple campaign available',
    type: getMultipleReferralCampaignSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async getMultipleReferralCampaigns() {
    return await this.service.getMultipleReferralCampaign();
  }

  @Get('campaign/:campaignId')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'Get single Campaign' })
  @ApiParam({
    name: 'campaignId',
    description: 'The campaignId whose data you want',
    type: String,
  })
  @ApiResponse({
    description: 'Get single campaign',
    type: getSingleReferralCampaignSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async getSingleReferralCampaign(
    @Param('campaignId', ParseUUIDPipe) campaignId: string,
  ) {
    return await this.service.getSingleReferralCampaign(campaignId);
  }
}
