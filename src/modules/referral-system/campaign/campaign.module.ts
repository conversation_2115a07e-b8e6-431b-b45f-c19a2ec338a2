import { Modu<PERSON> } from '@nestjs/common';
import { ReferralCampaignControllerForAdmin } from './controllers/admin.controller';
import { ReferralCampaignControllerForUser } from './controllers/user.controller';
import { CampaignProviderForUser } from './providers/user.provider';
import { ReferralCampaignRepositoryForAdmin } from './repositories/admin.repository';
import { ReferralCampaignRepositoryForUser } from './repositories/user.repository';
import { ReferralCampaignServiceForAdmin } from './services/admin.service';
import { ReferralCampaignServiceForUser } from './services/user.service';

@Module({
  controllers: [ReferralCampaignControllerForAdmin,ReferralCampaignControllerForUser],
  providers: [
              ReferralCampaignServiceForAdmin,
              ReferralCampaignRepositoryForAdmin,
              ReferralCampaignServiceForUser,
              ReferralCampaignRepositoryForUser,
              CampaignProviderForUser
            ],
  exports:[CampaignProviderForUser]
})
export class CampaignModule {}
