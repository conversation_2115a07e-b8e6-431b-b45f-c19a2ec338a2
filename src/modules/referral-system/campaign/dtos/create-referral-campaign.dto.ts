import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested
} from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import { CampaignStatus, ReferralCampaignMediaItemType, TaskEventType, TaskStatus } from '../../common/entities/referral-campaign.entity';

export class ReferralCampaignMediaItemDto {
  @ApiProperty({ enum: ReferralCampaignMediaItemType, example: 'image' })
  @IsEnum(ReferralCampaignMediaItemType)
  type: ReferralCampaignMediaItemType;

  @ApiProperty({ example: 'https://example.com/image.jpg' })
  @IsString()
  @IsNotEmpty()
  url: string;
}

export class ReferralCampaignMediaItemResponseDto {
  @Expose()
  @ApiProperty({ enum: ReferralCampaignMediaItemType })
  type: ReferralCampaignMediaItemType;

  @Expose()
  @ApiProperty({ example: 'https://example.com/image.jpg' })
  url: string;
}
export class TaskConfigDto {
    @ApiProperty({ required: false })
    @IsOptional()
    @IsNumber()
    count?: number;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsNumber()
    duration?: number;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    programId?: string;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    challengeId?: string;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsObject()
    metadata?: any;
}
export class CreateTaskDto {
    @ApiProperty({ enum: TaskEventType })
    @IsEnum(TaskEventType)
    type: TaskEventType;

    @ApiProperty()
    @IsBoolean()
    required: boolean;

    @ApiProperty()
    @IsNumber()
    order: number;

    @ApiProperty()
    @IsString()
    description: string;

    @ApiProperty()
    @IsNumber()
    points: number;

    @ApiProperty({ required: false })
    @IsOptional()
    @ValidateNested()
    @Type(() => TaskConfigDto)
    config?: TaskConfigDto;
}

export class RewardConfigDto {
    @ApiProperty()
    @IsNumber()
    referrerPoints: number;

    @ApiProperty()
    @IsNumber()
    refereePoints: number;
}

export class TaskProgressDto {
    @ApiProperty()
    taskId: string;

    @ApiProperty({ enum: TaskStatus })
    status: TaskStatus;

    @ApiProperty({ required: false })
    progress?: {
        current: number;
        target: number;
    };

    @ApiProperty()
    startedAt: Date;

    @ApiProperty({ required: false })
    completedAt?: Date;

    @ApiProperty()
    lastUpdated: Date;
}
export class CreateCampaignDto {
    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    name: string;

   @ApiProperty()
    @IsString()
    @IsNotEmpty()
    slug: string;

    @ApiProperty()
    @IsString()
    description: string;

    @ApiProperty()
    @IsNumber()
    validityDays: number;
  
    @ApiProperty({
    type: [String],
    required: false,
    description: 'List of rules shown to the user, each as a separate line',
    example: [
      "Use your unique link or code to share the coaching program.",
      "You can share via WhatsApp, Instagram, Facebook, email, or SMS.",
    ]
    })
    @IsOptional()
    @IsString({ each: true })
    rules?: string[];

    @ApiProperty()
    @IsNumber()
    completionDays: number;

    @ApiProperty()
    @IsBoolean()
    isSequential: boolean;

    @ApiProperty({ type: [CreateTaskDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateTaskDto)
    tasks: CreateTaskDto[];

    @ApiProperty()
    @ValidateNested()
    @Type(() => RewardConfigDto)
    baseRewardConfig: RewardConfigDto;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsNumber()
    maxRewardsPerDay?: number;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsNumber()
    maxRewardsTotal?: number;
  
    @ApiProperty({ required: false })
    @IsOptional()
    @IsNumber()
    rewardAmount?: number;

    // @ApiProperty({ required: false })
    // @IsOptional()
    // @IsDate()
    // @Type(() => Date)
    // startDate?: Date;

    // @ApiProperty({ required: false })
    // @IsOptional()
    // @IsDate()
    // @Type(() => Date)
    // endDate?: Date;

    @ApiProperty({ required: false })
    @IsOptional()
    @IsNumber()
    maxReferralCodeUse?: number;

    @ApiProperty({ example: true, required: false })
    @IsBoolean()
    @IsOptional()
    singleRewardPerReferee?: boolean;
  
    @ApiProperty({ required: false })
    @IsOptional()
    @IsNumber()
    monthlyCapPerReferrer?: number;
  
    @ApiProperty({
      type: [ReferralCampaignMediaItemDto],
      required: false,
      description: 'Array of media items (images/videos)',
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ReferralCampaignMediaItemDto)
    media?: ReferralCampaignMediaItemDto[];
}

export class UpdateCampaignDto extends CreateCampaignDto {
    @ApiProperty({ enum: CampaignStatus, required: false })
    @IsOptional()
    @IsEnum(CampaignStatus)
    status?: CampaignStatus;
}




export class CampaignResponseDto {
    
    @Expose()
    @ApiProperty()
    id: string;
    
    @Expose()
    @ApiProperty()
    name: string;
  
    @Expose()
    @ApiProperty()
    slug: string;
  
    @Expose()
    @ApiProperty()
    description: string;

    @Expose()
    @ApiProperty({ enum: CampaignStatus })
    status: CampaignStatus;
  
    @Expose()
    @ApiProperty({ type: [CreateTaskDto] })
    tasks: CreateTaskDto[];
  
    @Expose()
    @ApiProperty()
    isSequential: boolean;
  
    @Expose()
    @ApiProperty()
    rules: string[];
  
    @Expose()
    @ApiProperty()
  completionDays: number;
  
    @Expose()
    @ApiProperty()
    baseRewardConfig: RewardConfigDto;
    @Expose()
    @ApiProperty()
    validityDays: number;
    @Expose()
    @ApiProperty({ required: false })
    startDate?: Date;
    @Expose()
    @ApiProperty({ required: false })
    endDate?: Date;
    @Expose()
    @ApiProperty()
    totalRewardsIssued: number;
    @Expose()
    @ApiProperty()
    activeReferrals: number;
    @Expose()
    @ApiProperty()
    completedReferrals: number;
    @Expose()
    @ApiProperty()
    isActive: boolean;
    @Expose()
    @ApiProperty()
    createdAt: Date;
    @Expose()
    @ApiProperty()
    updatedAt: Date;
}



export class ReferralCampaignSuccessResponseDto
  implements ServiceSuccessResponse
{
  @Expose()
  @ApiProperty({ type: CampaignResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: CampaignResponseDto;
}
