import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
// import { ReferralCampaignType } from '../../common/entities/referral-campaign.entity';
import { ReferralCampaignMediaItemDto } from './create-referral-campaign.dto';

export class UpdateReferralCampaignRequestDto {
  @ApiProperty({ example: 'coach-program', description: 'Unique campaign slug' })
  @IsString()
  @IsOptional()
  slug?: string;

  @ApiProperty({ example: 'Share Coach Program' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ example: 'Refer friends and earn rewards!', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
  type: [String],
  required: false,
  description: 'List of rules shown to the user, each as a separate line',
  example: [
    "Use your unique link or code to share the coaching program.",
    "You can share via WhatsApp, Instagram, Facebook, email, or SMS.",
  ]
  })
  @IsOptional()
  @IsString({ each: true })
  rules?: string[];

  @ApiProperty({ example: 50, required: false })
  @IsNumber()
  @IsOptional()
  pointsReward?: number;

  @ApiProperty({ example: 30, required: false })
  @IsNumber()
  @IsOptional()
  pointsReferrer?: number;

  @ApiProperty({ example: 20, required: false })
  @IsNumber()
  @IsOptional()
  pointsReferee?: number;

  @ApiProperty({ example: 1000, required: false })
  @IsNumber()
  @IsOptional()
  maxPointsReward?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  monthlyCapPerReferrer?: number;

  @ApiProperty({ example: 30, required: false })
  @IsNumber()
  @IsOptional()
  validityDays?: number;

  @ApiProperty({ example: 200, required: false })
  @IsNumber()
  @IsOptional()
  rewardCapPerMonth?: number;

  @ApiProperty({ example: true, required: false })
  @IsBoolean()
  @IsOptional()
  singleRewardPerReferee?: boolean;

  @ApiProperty({ example: 10, required: false })
  @IsNumber()
  @IsOptional()
  maxReferralCodeUse?: number;

  @ApiProperty({ example: true, required: false })
  @IsBoolean()
  @IsOptional()
  isActive: boolean;

  // @ApiProperty({
  // enum: ReferralCampaignType,
  // required: false,
  // example: 'newsfeed',
  // })
  // @IsOptional()
  // @IsString()
  // campaignType?: ReferralCampaignType;

  @ApiProperty({
    type: [ReferralCampaignMediaItemDto],
    required: false,
    description: 'Array of media items (image/video)',
  })
  @IsOptional()
  media?: ReferralCampaignMediaItemDto[];


//  @ApiProperty({
//   example: '2025-05-01T00:00:00.000Z',
//   required: false,
//   type: String,
//   format: 'date-time',
//   description: 'Start date of the campaign in ISO format',})
//   @IsOptional()
//   startDate?: Date;

//   @ApiProperty({
//   example: '2025-06-01T00:00:00.000Z',
//   required: false,
//   type: String,
//   format: 'date-time',
//   description: 'End date of the campaign in ISO format',
//   })
//   @IsOptional() 
//   endDate?: Date;
}
