    import { ApiProperty } from "@nestjs/swagger";
import { Expose, Transform, Type } from "class-transformer";
import { IsEnum, IsInt, IsNotEmpty, IsObject, IsOptional, Min } from "class-validator";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";
// import { ReferralCampaignType } from "../../common/entities/referral-campaign.entity";
import { CampaignStatus } from "../../common/entities/referral-campaign.entity";
import { CreateTaskDto, RewardConfigDto } from "./create-referral-campaign.dto";

export enum CampaignStatusForAdmin {
  ANY = 'any',
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}
export class GetReferralCampaignQueryDtoForAdmin {

  @ApiProperty({ required: false, enum: CampaignStatusForAdmin })
  @IsEnum(CampaignStatusForAdmin)
  @IsOptional()
  @Transform(({ value }) => value ? value : undefined)
  adminCampaignStatus?:CampaignStatusForAdmin ;

  @ApiProperty({ required: false, type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  offset?: number;

  @ApiProperty({ required: false, type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number;

}
export class GetReferralCampaignResponseDto {
     @Expose()
     @ApiProperty()
     id: string;
     
     @Expose()
     @ApiProperty()
     name: string;
   
     @Expose()
     @ApiProperty()
     slug: string;
   
     @Expose()
     @ApiProperty()
     description: string;
 
     @Expose()
     @ApiProperty({ enum: CampaignStatus })
     status: CampaignStatus;
   
     @Expose()
     @ApiProperty({ type: [CreateTaskDto] })
     tasks: CreateTaskDto[];
   
     @Expose()
     @ApiProperty()
     isSequential: boolean;
   
     @Expose()
     @ApiProperty()
     rules: string[];
   
     @Expose()
     @ApiProperty()
     completionDays: number;
   
     @Expose()
     @ApiProperty()
     baseRewardConfig: RewardConfigDto;
  
     @Expose()
     @ApiProperty()
    validityDays: number;
  
     @Expose()
     @ApiProperty({ required: false })
     startDate?: Date;
     
     @Expose()
     @ApiProperty({ required: false })
     endDate?: Date;
    
     @Expose()
     @ApiProperty({ required: false })
    totalRewardsIssued: number;
     
    @Expose()
    @ApiProperty({ required: false })
    monthlyCapPerReferrer?: number;
  
    @Expose()
    @ApiProperty({ required: false })
    rewardAmount?: number;
     
    @Expose()
    @ApiProperty({ required: false })
    maxRewardsTotal?: number;
     
     @Expose()
     @ApiProperty({ required: false })
     activeReferrals: number;
    
     @Expose()
     @ApiProperty({ required: false })
     completedReferrals: number;
    
     @Expose()
     @ApiProperty({ required: false })
     isActive: boolean;
    
     @Expose()
     @ApiProperty()
     createdAt: Date;
     
     @Expose()
     @ApiProperty()
     updatedAt: Date;
}


export class getSingleReferralCampaignSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: GetReferralCampaignResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: GetReferralCampaignResponseDto;
}

export class getMultipleReferralCampaignSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: () => [GetReferralCampaignResponseDto] })
  @IsObject()
  @IsNotEmpty()
  data: GetReferralCampaignResponseDto[];
}