import { Injectable } from "@nestjs/common";
import { ReferralCampaignEntity } from "../../common/entities/referral-campaign.entity";
import { ReferralCampaignRepositoryForUser } from "../repositories/user.repository";

@Injectable()
export class CampaignProviderForUser {
     constructor(private readonly repository: ReferralCampaignRepositoryForUser) { }
    
      async getSingleCoachProgram(campaignId: string): Promise<ReferralCampaignEntity> {
        return await this.repository.getReferralCampaign(campaignId);
      }
 }