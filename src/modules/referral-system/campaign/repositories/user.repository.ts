import { Injectable } from "@nestjs/common";
import { ReferralCampaignModel } from "../../common/db/referral-campaign.model";
import { CampaignStatus, ReferralCampaignEntity } from "../../common/entities/referral-campaign.entity";

@Injectable()
export class ReferralCampaignRepositoryForUser{

  async getReferralCampaigns(): Promise<ReferralCampaignEntity[]> {
      const searchQuery = { isDeleted: false , status: CampaignStatus.ACTIVE  };
      const campaignDoc = await ReferralCampaignModel.find(searchQuery).exec();
      return campaignDoc.map(item => item.toObject());
  }

  async getReferralCampaign(campaignId: String): Promise<ReferralCampaignEntity | null> {
    const searchQuery = { id: campaignId, isDeleted: false , status: CampaignStatus.ACTIVE  };
    const referralCampaignDoc = await ReferralCampaignModel.findOne(searchQuery).exec();
    return referralCampaignDoc !== null ? referralCampaignDoc.toObject() : null;
  }


}