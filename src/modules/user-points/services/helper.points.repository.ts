import { Injectable } from '@nestjs/common';
import { PipelineStage } from 'mongoose';

@Injectable()
export class HelperPointsRepository {
  userPointsLeaderBoardAggQuery(offset: number, limit: number): any[] {
    return [
      {
        $setWindowFields: {
          sortBy: { points: -1 },
          output: {
            ranking: { $rank: {} }, // Add explicit ranking using $rank
          },
        },
      },
      {
        $sort: {
          ranking: 1, // Sort by ranking first
          userId: 1, // Then by userId for consistent tie-breaking
        },
      },
      {
        $skip: Number(offset),
      },
      {
        $limit: Number(limit),
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: 'id',
          as: 'userInfo',
        },
      },
      {
        $project: {
          _id: 0,
          userId: 1,
          points: 1,
          // ranking: 1, // Include the ranking in the output
          name: { $first: '$userInfo.name' },
          image: { $first: '$userInfo.image.profile' },
        },
      },
    ];
  }

  pointsTransactionHistoryAggQuery(
    userId: string,
    offset: number,
    limit: number,
    filter = 'all',
  ): PipelineStage[] {
    // Create match stage based on filter
    const matchStage: any = {
      userId: userId,
    };

    // If filter is 'monthly', add date condition for current month
    if (filter === 'monthly') {
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastDayOfMonth = new Date(
        now.getFullYear(),
        now.getMonth() + 1,
        0,
        23,
        59,
        59,
        999,
      );

      matchStage.createdAt = {
        $gte: firstDayOfMonth,
        $lte: lastDayOfMonth,
      };
    }

    return [
      {
        $match: matchStage,
      },
      {
        $sort: {
          createdAt: -1 as 1 | -1,
        },
      },
      {
        $skip: Number(offset),
      },
      {
        $limit: Number(limit),
      },
      {
        $project: {
          _id: 0,
          id: 1,
          type: 1,
          points: 1,
          pointSourceType: 1,
          pointSourceId: 1,
          pointSourceName: 1,
          createdAt: 1,
        },
      },
    ];
  }

  userPointsAggQuery(): any[] {
    return [
      // Sort users by points in descending order
      {
        $sort: {
          points: -1,
        },
      },
      // Calculate the rank based on sorted points
      {
        $setWindowFields: {
          sortBy: { points: -1 }, // Ensure sorting is descending
          output: {
            userRanking: { $rank: {} }, // Generate the rank
          },
        },
      },
      // Lookup user information from 'users' collection
      // Project the necessary fields (excluding _id)
      {
        $project: {
          _id: 0,
          userId: 1,
          points: 1,
          userRanking: 1,
        },
      },
      // To calculate the total number of users (for totalRanking)
      {
        $facet: {
          totalUsers: [{ $count: 'total' }],
        },
      },
    ];
  }
}
