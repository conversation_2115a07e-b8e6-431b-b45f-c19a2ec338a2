import { Injectable } from "@nestjs/common";
import { UserActivityStreakService } from "../services/user-activity-streak.service";

@Injectable()
export class UserActivityStreakProvider {
  constructor(private readonly userActivityStreakService: UserActivityStreakService) { }

  async addSessionRecord(userId: string, startTime: Date, endTime: Date, millis: number): Promise<void> {
    await this.userActivityStreakService.saveSession(userId, startTime, endTime, millis);
  }
}
