import { <PERSON>, Get, HttpStatus, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { OnlyAuthGuard, OnlyRoleGuard, Role, Roles } from 'src/authentication/guards/auth-role.guard';
import { GetUserMonthlyStreakSuccessResponseDto, GetUserWeeklyStreakSuccessResponseDto } from '../dtos/get-activity-streak.dto';
import { GetUserStreakHistoryQueryDto, GetUserStreakHistorySuccessResponseDto } from '../dtos/get-streak-history.dto';
import { UserActivityStreakService } from '../services/user-activity-streak.service';

@ApiBearerAuth()
@ApiTags('User Activity Streak')
@Controller('user-activity-streak')
export class UserActivityStreakControllerForUser {
  constructor(private readonly userActivityStreakService: UserActivityStreakService) {}

  @Get('weekly-streak')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'Get the weekly streak info of a user' })
  @ApiQuery({
    name: 'userId',
    description: 'The user id',
    type: String,
    required: true,
  })
  @ApiResponse({
    description: 'Returns the weekly streak info of the user',
    type: GetUserWeeklyStreakSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async getWeeklyStreakInfo(@Query('userId') userId: string) {
    return await this.userActivityStreakService.getWeeklyStreakInfo(userId);
  }

  @Get('monthly-streak')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'Get the monthly streak info of a user' })
  @ApiQuery({
    name: 'userId',
    description: 'The user id',
    type: String,
    required: true,
  })
  @ApiQuery({
    name: 'monthsBack',
    description: 'Number of months to look back (0 for current month)',
    type: Number,
    required: false,
  })
  @ApiResponse({
    description: 'Returns the monthly streak info of the user',
    type: GetUserMonthlyStreakSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async getMonthlyStreakInfo(
    @Query('userId') userId: string,
    @Query('monthsBack', ParseIntPipe) monthsBack = 0
  ) {
    return await this.userActivityStreakService.getMonthlyStreakInfo(userId, monthsBack);
  }

  @Get('streak-history')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'Get the streak history of a user' })
  @ApiQuery({
    name: 'userId',
    description: 'The user id',
    type: String,
    required: true,
  })
  @ApiQuery({
    name: 'offset',
    description: 'offset for pagination',
    type: Number,
    required: false
  })
  @ApiQuery({
    name: 'limit',
    description: 'limit for pagination',
    type: Number,
    required: false
  })
  @ApiResponse({
    description: 'Returns the streak history of the user',
    type: GetUserStreakHistorySuccessResponseDto,
    status: HttpStatus.OK,
  })
  async getUserStreakHistory(@Query() queryDto: GetUserStreakHistoryQueryDto) {
    const offset = queryDto.offset ?? 0;
    const limit = queryDto.limit ?? 20;
    const activities = await this.userActivityStreakService.getUserStreakHistory(queryDto.userId, offset, limit);
    return { data: { activities } };
  }
}