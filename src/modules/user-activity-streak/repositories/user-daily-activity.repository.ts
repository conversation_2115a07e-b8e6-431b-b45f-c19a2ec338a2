import { Injectable } from '@nestjs/common';
import { UserDailyActivityModel } from 'src/database/user/userDailyActivity.model';
import { UserDailyActivity } from 'src/entity/activity';

@Injectable()
export class UserDailyActivityRepository {

  async addMinutes(userId: string, date: string, startTime: Date, endTime: Date, minutes: number): Promise<UserDailyActivity> {
    const doc = await UserDailyActivityModel.findOneAndUpdate(
      { userId, date },
      { 
        $inc: { activityDuration: minutes },
        $push: { activitySessionHistory: { startTime, endTime, minutes } },
        $setOnInsert: { activityMetRequirement: false }
      },
      { new: true, upsert: true }
    ).exec();
    return doc.toObject();
  }

  async updateActivityMetRequirement(userId: string, date: string, activityMetRequirement: boolean): Promise<UserDailyActivity> {
    const doc = await UserDailyActivityModel.findOneAndUpdate(
      { userId, date },
      { $set: { activityMetRequirement: activityMetRequirement } },
      { new: true, upsert: true }
    ).exec();
    return doc.toObject();
  }

  async getUserActivityHistory(userId: string, offset: number, limit: number): Promise<UserDailyActivity[] | null> {
    const activities = await UserDailyActivityModel.find({ userId })
      .sort({ date: -1 }) // Sort by date in descending order (newest first)
      .skip(offset)
      .limit(limit)
      .exec();
    
    return activities?.map(doc => doc.toObject()) || null;
  }

  async getTodayActivity(userId: string): Promise<UserDailyActivity | null> {
    const today = new Date().toISOString().slice(0, 10);
    const activity = await UserDailyActivityModel.findOne({ userId, date: today }).exec();
    return activity?.toObject() || null;
  }

    async getActivityStreakWithTimeline(userId: string, startDate: Date, endDate: Date): Promise<UserDailyActivity[] | null> {
      const formattedStartDate = startDate.toISOString().split('T')[0];
      const formattedEndDate = endDate.toISOString().split('T')[0];
      const streak = await UserDailyActivityModel.find({ userId, date: { $gte: formattedStartDate, $lte: formattedEndDate } })
        .sort({ date: 1 })
        .exec();
      return streak.map(doc => doc.toObject());
    }
}
