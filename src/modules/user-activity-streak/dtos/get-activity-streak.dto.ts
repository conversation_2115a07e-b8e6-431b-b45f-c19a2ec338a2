import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";
import { IsArray, IsBoolean, IsDate, IsNotEmpty, IsNumber, IsObject, IsString } from "class-validator";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";

export class StreakInfoDto {
  @Expose()
  @ApiProperty({ required: true, type: Date })
  @IsDate()
  @IsNotEmpty()
  date: Date;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isStreak: boolean;
}

export class GetUserWeeklyStreakResponseDtoForUser {

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNumber()
  @IsNotEmpty()
  currentStreak: number;

  @Expose()
  @ApiProperty({ required: true, type: [StreakInfoDto] })
  @Type(() => StreakInfoDto)
  @IsArray()
  @IsNotEmpty()
  streakHistory: StreakInfoDto[];
}

export class GetUserMonthlyStreakResponseDtoForUser {

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @Expose()
  @ApiProperty({ required: true, type: [StreakInfoDto] })
  @Type(() => StreakInfoDto)
  @IsArray()
  @IsNotEmpty()
  streakHistory: StreakInfoDto[];
}

export class GetUserWeeklyStreakSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: GetUserWeeklyStreakResponseDtoForUser })
  @Type(() => GetUserWeeklyStreakResponseDtoForUser)
  @IsObject()
  @IsNotEmpty()
  data: GetUserWeeklyStreakResponseDtoForUser;
}

export class GetUserMonthlyStreakSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: GetUserMonthlyStreakResponseDtoForUser })
  @Type(() => GetUserMonthlyStreakResponseDtoForUser)
  @IsObject()
  @IsNotEmpty()
  data: GetUserMonthlyStreakResponseDtoForUser;
}

  