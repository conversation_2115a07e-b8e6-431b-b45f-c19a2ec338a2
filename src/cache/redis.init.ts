import { redisCacheConfig } from 'config/cache';
import { createClient, RedisClientType } from 'redis';
let redisClient: RedisClientType;

export async function connectToRedis() {
  try {
    // Create a Redis object.
    redisClient = createClient({
      url: redisCacheConfig.redis_url,
    });

    redisClient.on('error', (err) => console.error('Redis Client Error', err));
    redisClient.on('connect', () => console.log('Redis Client connected'));
    redisClient.on('disconnect', (err) =>
      console.error('Redis Client disconnected!!!', err),
    );

    await redisClient.connect();
  } catch (error: any) {
    console.log(error.message);
    return null;
  }
}

export async function getRedisClient() {
  try {
    if (!redisClient) {
      await connectToRedis();
    }
    return redisClient;
  } catch (err) {
    console.error('Error getting redis client', err);
  }
}
