/* eslint-disable @typescript-eslint/no-unused-vars */
import { redisCacheConfig } from 'config/cache';
import { UserNewsFeedStatsModel } from 'src/database/user/userStats.model';
import { Post, PostComment, PostLiker } from 'src/entity/post';
import { AdvertisingProduct } from 'src/entity/product';
import { UserNewsFeedStats } from 'src/entity/user';
import { RedisCacheHelper } from '../helper';
import {
  getAdvertisingProducts,
  findBuddyIds,
  findFitBuddiesPosts,
  findFitFollowEePosts,
  findPopularPosts,
  postsAggregate,
  updateUserNewsFeedStats,
  getAdvertisingProductsCount,
  getSinglePost,
  blockList,
} from './database';
const ONE_DAY_IN_MILLISEC = 24 * 60 * 60 * 1000;

const nullAndEmptyObj = {
  userId: null,
  content: null,
  images: [],
  videos: [],
  privacy: null,
  totalLikes: 0,
  totalReactions: 0,
  totalComments: 0,
  totalShares: 0,
  location: null,
  createdAt: null,
  userInfo: null,
  liked: null,
  reactionType: null,
  comments: [],
  reactions: null
};
export interface PostIdsObj {
  postId: string;
  liked: boolean;
  reactionType?: string | null;  
  comments: PostComment[];
  totalLikes: number;
  totalReactions: number;
  totalComments: number;
  totalShares: number;
  likersInfo?: PostLiker[];
  totalReactionerInfo?: PostLiker[];
  reactionTypeUserInfo?: PostLiker[];
  reactions: null
}

function getToday(): Date {
  const today = new Date();
  today.setHours(23, 59, 59, 999);
  return today;
}

export async function getMyFeed(userId: string, refresh: boolean) {
  const organize = (allPosts) => {
    let adv = [];
    const posts = [];
    const result = [];

    if (allPosts?.length > 0) {
      allPosts.forEach((element) => {
        if (element.productInfo) adv.push(element);
        else posts.push(element);
      });

      adv = shuffleArray(adv);

      let ind = 0;

      for (let i = 0; i < posts.length; i++) {
        if (i > 0 && i % 5 == 0 && adv[ind]) {
          result.push(adv[ind]);
          ind++;
        }
        result.push(posts[i]);
      }
      return result;
    }

    return allPosts;
  };

  try {
    // if refresh delete user cached data and reset UserNewsFeedStats to get latest build posts
    if (refresh) {
      await RedisCacheHelper.deleteData(
        `${redisCacheConfig.user_feed_channel}/${userId}`,
      );
    }
    const [userFeedStats, totalAdvertisingProducts] = await Promise.all([
      refresh
        ? await UserNewsFeedStatsModel.findOneAndUpdate(
            { userId },
            {
              buddyListOffset: 0,
              postDateOffset: 0,
              followEePostListOffset: 0,
              advertisingProductsOffset: 0,
            },
          ).lean()
        : await UserNewsFeedStatsModel.findOne({ userId }).lean(),
      await getAdvertisingProductsCount(),
    ]);

   

    let postIdsObjArray: PostIdsObj[] = [];
    // pop from right side. Like a queue
    postIdsObjArray = await RedisCacheHelper.executeCommand<PostIdsObj>([
      'RPOP',
      `${redisCacheConfig.user_feed_channel}/${userId}`,
      `${redisCacheConfig.postIds_default_limit}`,
    ]);

   

    

 

    if (!postIdsObjArray || postIdsObjArray.length === 0) {
     

      await buildMyFeed(userId, userFeedStats, totalAdvertisingProducts);
      postIdsObjArray = await RedisCacheHelper.executeCommand<PostIdsObj>([
        'RPOP',
        `${redisCacheConfig.user_feed_channel}/${userId}`,
        `${redisCacheConfig.postIds_default_limit}`,
      ]);

      // console.log('posts ids object array length',postIdsObjArray.length )
      // console.log('post ids object array', postIdsObjArray )


    }

    const posts = [];
    
    let advertising_products_count = 0;
    if (postIdsObjArray && postIdsObjArray.length) {
      
      await Promise.all(
        postIdsObjArray.map(async (postIdsObj: PostIdsObj) => {
          const advertisingProducts = await RedisCacheHelper.getData<any>(
            `${redisCacheConfig.advertising_products_channel}/${postIdsObj.postId}`,
          );

          if (advertisingProducts && advertisingProducts.productInfo) {
            // check the advertising products is less than the totalAdvertisingProducts count
            advertising_products_count < totalAdvertisingProducts &&
              posts.push({
                type: 'ADVERTISING_PRODUCT',
                id: advertisingProducts.productId,
                productInfo: {
                  name: advertisingProducts.productInfo.info?.name,
                  price: advertisingProducts.productInfo.info?.price,
                  oldPrice: advertisingProducts.productInfo.info?.oldPrice,
                  photos: advertisingProducts.productInfo?.photos,
                  avgRating: advertisingProducts.productInfo?.avgRating,
                },
                ...nullAndEmptyObj,
              });

            //increment advertising products
            advertising_products_count++;
          } else {
            // console.log('post ids object', postIdsObj )
            const post =
              (await RedisCacheHelper.getData<Post>(
                `${redisCacheConfig.post_channel}/${postIdsObj.postId}`,
              )) || (await getSinglePost(postIdsObj.postId));

              // console.log('post redis', `${redisCacheConfig.post_channel}/${postIdsObj.postId}`)
              // console.log('post id object reactiontype', postIdsObj.likersInfo)
              // console.log('post id object ', postIdsObj)

              // console.log('post',post)

            post &&
              post.privacy !== 'PRIVATE' &&
              posts.push({
                ...post,
                totalLikes: postIdsObj.totalLikes || post.totalLikes,
                totalReactions:
                  postIdsObj.totalReactions || post.totalReactions,
                totalComments: postIdsObj.totalComments || post.totalComments,
                totalShares: postIdsObj.totalShares || post.totalShares,
                liked: postIdsObj.liked || false,
                comments: postIdsObj.comments || [],
                productInfo: null,
                likersInfo: postIdsObj.likersInfo,
                totalReactionerInfo: postIdsObj.totalReactionerInfo,
                reactionType: postIdsObj.reactionType || null,
                reactions: postIdsObj.reactions ,
                reactionTypeUserInfo:postIdsObj.reactionTypeUserInfo
              });
          }



        }),
      );
    }

    // console.log("all posts",posts )

    return organize(posts);
  } catch (err) {
    console.error('getMyFeed err: ', err);
    return null;
  }
}

export function buildMyFeed(
  userId: string,
  userFeedStats: UserNewsFeedStats,
  totalAdvertisingProducts: number,
) {
  try {
    return new Promise(async (resolve, _reject) => {
      let totalPostFound = 0;

      let {
        buddyListOffset = 0,
        postDateOffset = 0,
        advertisingProductsOffset = 0,
      } = userFeedStats || {};
      const totalBuddies = userFeedStats?.totalBuddies || 0;

      while (
        buddyListOffset < (totalBuddies || Number.MAX_SAFE_INTEGER) &&
        postDateOffset < redisCacheConfig.max_post_date
      ) {
        // Example: get last 2 days post for first 10 buddies
        const today = getToday();
        const toDate = new Date(
          today.getTime() - postDateOffset * ONE_DAY_IN_MILLISEC,
        );
        const fromDate = new Date(
          today.getTime() -
            (postDateOffset + redisCacheConfig.post_date_default_offset) *
              ONE_DAY_IN_MILLISEC,
        );

        const { fitBuddyIds, followEeBuddyIds } =
          totalBuddies &&
          (await findBuddyIds(userId, buddyListOffset, fromDate, toDate));

        postDateOffset += redisCacheConfig.post_date_default_offset;
        totalBuddies &&
          (buddyListOffset += redisCacheConfig.buddy_default_offset);

        // start from 0 again to get the next two days posts of other buddies
        if (totalBuddies && buddyListOffset >= totalBuddies) {
          buddyListOffset = 0;
        }

        const blockedIds = await blockList(userId);
        const [popularPosts, advertisingProducts, buddyPosts, followEePosts] =
          await Promise.all([
            await findPopularPosts(
              fromDate,
              toDate,
              blockedIds,
              fitBuddyIds || [],
              followEeBuddyIds || [],
            ),
            await getAdvertisingProducts(advertisingProductsOffset),
            totalBuddies &&
              (await findFitBuddiesPosts(fitBuddyIds, fromDate, toDate)),
            totalBuddies &&
              (await findFitFollowEePosts(followEeBuddyIds, fromDate, toDate)),
          ]);

        const mappedPosts = new Map<string, Post>();
        const postIdsSet = new Set<string>([
          ...(popularPosts || []).map((post: Post) => {
            mappedPosts.set(post.id, post);
            return post.id;
          }),
          ...(advertisingProducts || []).map(
            (product: AdvertisingProduct) => product.productId,
          ),
          ...(buddyPosts || []).map((post: Post) => {
            !mappedPosts.get(post.id) && mappedPosts.set(post.id, post);
            return post.id;
          }),
          ...(followEePosts || []).map((post: Post) => {
            !mappedPosts.get(post.id) && mappedPosts.set(post.id, post);
            return post.id;
          }),
        ]);

        // start from 0 again if all advertising product is listed.
        advertisingProductsOffset >= totalAdvertisingProducts
          ? (advertisingProductsOffset = 0)
          : (advertisingProductsOffset += advertisingProducts.length);

        if (postIdsSet.size) {
          // Shuffles postIds(randomize)
          const postIds = shuffleArray([...postIdsSet] as string[]);

          const postIdsWithComments = await postsAggregate(
            userId,
            postIds,
            mappedPosts,
          );

        //   postIdsWithComments.forEach(post => {
        //     console.log(`${userId} user likes the post ${post.postId} and liked value is ${post.liked} and reaction type is ${post.reactionType}`);
        // });

          // console.log('postIdwith comments', postIdsWithComments)
         
          

          if (postIdsWithComments?.length) {
            await RedisCacheHelper.leftPushData<PostIdsObj>(
              `${redisCacheConfig.user_feed_channel}/${userId}`,
              postIdsWithComments,
            );
          }

          totalPostFound += postIds.length;
          // 20 posts has been found then send acknowledgment to the caller,
          // so that he can send the chunk of posts to the user
          // and the build process continues. no return from here
          if (totalPostFound >= 20) {
            resolve(true);
          }
        }

        // if 300 posts have been cached already then stop building the cache
        if (totalPostFound >= redisCacheConfig.max_post_cache_limit) {
          // save the last processed offset, so that the next cache build process can start after that
          await updateUserNewsFeedStats<Partial<UserNewsFeedStats>>(userId, {
            buddyListOffset,
            postDateOffset,
            advertisingProductsOffset,
            lastBuildAt: new Date(),
          });
          return resolve(true);
        }
      }
      // Reached the end of the max limit of post date (Ex: last 7 days) for all buddies
      // now restart from current date again
      postDateOffset >= redisCacheConfig.max_post_date && (postDateOffset = 0);

      // start from 0 again to get the next two days posts of other buddies
      buddyListOffset >= totalBuddies && (buddyListOffset = 0);

      // save the last processed offset, so that the next cache build process can start after that
      await updateUserNewsFeedStats<Partial<UserNewsFeedStats>>(userId, {
        buddyListOffset,
        postDateOffset,
        advertisingProductsOffset,
        lastBuildAt: new Date(),
      });
      return resolve(true);
    });
  } catch (err) {
    console.error('buildMyFeed err: ', err);
    return false;
  }
}

/**
 * Shuffles array.
 * @param {Array} arr items An array containing the postIds.
 */
export function shuffleArray(arr: string[]) {
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr;
}
