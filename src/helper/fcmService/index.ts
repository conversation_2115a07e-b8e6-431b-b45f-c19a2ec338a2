import { Injectable } from '@nestjs/common';
import {
  FcmSubscriptionResponse,
  FcmTopicResponse,
  IFcmService,
  IndividualFcmRequest,
  PushPriority,
  TopicFcmRequest,
} from './fcm.service.interface';
import * as admin from 'firebase-admin';
import { fireBaseConfig } from 'config/fcm';

@Injectable()
export class FcmService implements IFcmService {
  constructor() {
    !admin.apps.length &&
      admin.initializeApp({
        credential: admin.credential.cert(
          JSON.parse(JSON.stringify(fireBaseConfig)),
        ),
      });
  }

  async sendToIndividual({
    token,
    title,
    body,
    documentId,
    data,
    isHighPriority,
    ttl,
  }: IndividualFcmRequest): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
          title,
          body,
        },
        android: {
          // notification: {
          //   title,
          //   body,
          // },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        token,
      };
      await admin.messaging().send(payload);
      return true;
    } catch (error) {
      return false;
    }
  }

  async sendToMany(
    tokens: string[],
    title: string,
    body: string,
    documentId: string,
    data: object,
    isHighPriority: boolean,
    ttl: number,
  ): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
        },
        android: {
          notification: {
            title,
            body,
          },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        tokens,
      };
      await admin.messaging().sendMulticast(payload);
      return true;
    } catch (error) {
      console.error('FCM sendToMany error:', error?.message || error);
      return false;
    }
  }

  async sendToTopic(fcmRequest: TopicFcmRequest): Promise<FcmTopicResponse> {
    const { title, body, documentId, data, topic } = fcmRequest;
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...Object.fromEntries(
            Object.entries(data || {}).map(([key, value]) => [
              key,
              value === null || value === undefined ? '' : String(value)
            ])
          ),
          documentId: documentId === null || documentId === undefined ? '' : String(documentId),
          title: String(title),
          body: String(body),
        },
        android: {
          notification: {
            title,
            body,
            sound: 'default',
            channelId: 'default',
            priority: 'high' as const,
            defaultSound: true,
            defaultVibrateTimings: true,
          },
          priority: PushPriority.HIGH,
          collapseKey: '',
          ttl: 600, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + 600000) / 1000).toFixed(),
            'apns-priority': '10',
            'apns-push-type': 'alert',
          },
          payload: {
            aps: {
              alert: {
                title,
                body,
              },
              sound: 'default',
              badge: 1,
            },
            // Include custom data for iOS (convert all values to strings)
            ...Object.fromEntries(
              Object.entries(data || {}).map(([key, value]) => [
                key,
                value === null || value === undefined ? '' : String(value)
              ])
            ),
          },
        },
        topic,
      };
      const response = await admin.messaging().send(payload);
      return { messageId: response, topic, success: true };
    } catch (error) {
      console.error('FCM sendToTopic error:', error?.message || error);
      return { success: false, error: error?.message || error };
    }
  }

  async subscribeNotificationTopic(tokens: string | string[], topic: string): Promise<FcmSubscriptionResponse> {
    try {
      const response = await admin.messaging().subscribeToTopic(tokens, topic);
      const tokenCount = Array.isArray(tokens) ? tokens.length : 1;

      return {
        success: true,
        topic,
        tokenCount,
        successCount: response.successCount,
        failureCount: response.failureCount,
        errors: response.errors
      };
    } catch (error) {
      console.error('FCM subscribeNotificationTopic error:', error?.message || error);
      return { success: false, error: error?.message || error };
    }
  }

  async unsubscribeNotificationTopic(tokens: string | string[], topic: string): Promise<FcmSubscriptionResponse> {
    try {
      const response = await admin
        .messaging()
        .unsubscribeFromTopic(tokens, topic);
      const tokenCount = Array.isArray(tokens) ? tokens.length : 1;

      return {
        success: true,
        topic,
        tokenCount,
        successCount: response.successCount,
        failureCount: response.failureCount,
        errors: response.errors
      };
    } catch (error) {
      console.error('FCM unsubscribeNotificationTopic error:', error?.message || error);
      return { success: false, error: error?.message || error };
    }
  }
}
