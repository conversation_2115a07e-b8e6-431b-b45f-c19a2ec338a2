import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { ChatGroupInfo, ChatGroupMember } from 'src/entity/chatGroupInfo';

const ChatGroupMembersSchema = new Schema<ChatGroupMember>(
  {
    userId: String,
  },
  { timestamps: false, versionKey: false, _id: false },
);

const ChatGroupInfoSchema = new Schema<ChatGroupInfo>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    name: {
      type: String,
      default: 'New Group',
    },
    description: {
      type: String,
      default: null,
    },
    image: {
      type: String,
      default: null,
    },
    members: {
      type: [ChatGroupMembersSchema],
      default: null,
    },
    admins: {
      type: [ChatGroupMembersSchema],
      default: null,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const ChatGroupInfoModel = model<ChatGroupInfo>(
  'chat-group-info',
  ChatGroupInfoSchema,
);
export { ChatGroupInfoModel };
