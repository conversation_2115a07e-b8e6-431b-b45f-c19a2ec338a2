import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { OneToOneChat } from 'src/entity/oneToOneChat';

const OneTwoOneChatSchema = new Schema<OneToOneChat>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    senderId: {
      type: String,
    },
    receiverId: {
      type: String,
    },
    type: {
      type: String,
      enum: ['text', 'image'],
      default: 'text',
    },
    content: {
      type: String,
      default: null,
    },
    isLastSeen: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

OneTwoOneChatSchema.index({ senderId: 1, receiverId: 1 });

const OneToOneChatModel = model<OneToOneChat>(
  'one-to-one-chat',
  OneTwoOneChatSchema,
);
export { OneToOneChatModel };
