import { model, Schema } from 'mongoose';
import { PostReaction, PostReactionsType } from 'src/entity/post';

const PostReactionSchema = new Schema<PostReaction>(
  {
    userId: {
      type: String,
      index: true,
      required: true,
    },
    postId: {
      type: String,
      index: true,
      required: true,
    },
    type: {
      type: String,
      trim: true,
      required: true,
      enum: PostReactionsType,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const PostReactionModel = model<PostReaction>(
  'post-reaction',
  PostReactionSchema,
);
export { PostReactionModel };
