import { model, Schema } from 'mongoose';
import { TaskPostReaction } from 'src/entity/task-post-reaction';

const TaskPostReactionSchema = new Schema<TaskPostReaction>(
  {
    userId: {
      type: String,
      index: true,
      required: true,
    },
    postId: {
      type: String,
      index: true,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const TaskPostReactionModel = model<TaskPostReaction>(
  'task-post-reaction',
  TaskPostReactionSchema,
);

export { TaskPostReactionModel };
