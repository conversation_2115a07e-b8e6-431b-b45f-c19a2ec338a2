import { model, Schema } from 'mongoose';
import { VisitedProduct } from 'src/entity/product';

const VisitedProductSchema = new Schema<VisitedProduct>(
  {
    userId: {
      type: String,
      unique: true,
    },
    products: [
      {
        id: String,
        _id: false,
      },
    ],
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const VisitedProductModel = model<VisitedProduct>(
  'visited-product',
  VisitedProductSchema,
);
export { VisitedProductModel };
