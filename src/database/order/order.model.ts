import { model, Schema } from 'mongoose';
import {
  OrderEntity,
  OrderStatusEnum,
  PaymentMethodEnum,
  PaymentStatusEnum,
  ShippingStatusEnum,
} from 'src/entity/order';

const AddressSchema = {
  firstName: {
    type: String,
    trim: true,
    required: true,
  },
  lastName: {
    type: String,
    trim: true,
    required: true,
  },
  email: {
    type: String,
    required: true,
    trim: true,
  },
  addressLine1: {
    type: String,
    trim: true,
    required: true,
  },
  addressLine2: {
    type: String,
    trim: true,
  },
  city: {
    type: String,
    trim: true,
    required: true,
  },
  state: {
    type: String,
    trim: true,
  },
  country: {
    type: String,
    trim: true,
    required: false,
  },
  postCode: {
    type: String,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
    required: true,
  },
};

const ProductSchema = new Schema(
  {
    productId: {
      type: String,
      trim: true,
      required: true,
    },
    name: {
      type: String,
      trim: true,
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    totalPrice: {
      type: Number,
      required: false,
    },
    quantity: {
      type: Number,
      trim: true,
      required: true,
    },
    quantityShipped: {
      type: Number,
      default: 0,
    },
    sku: {
      type: String,
      trim: true,
      required: true,
    },
    photo: {
      type: String,
      default: null,
    },
    size: {
      type: String,
      default: null,
    },
    color: {
      type: String,
      default: null,
    },
    parcel: {
      length: { type: Number, default: null },
      height: { type: Number, default: null },
      width: { type: Number, default: null },
      weight: { type: Number, default: null },
    },
  },
  {
    _id: false,
  },
);

const OrderSchema = new Schema<OrderEntity>(
  {
    orderId: {
      type: String,
      unique: true,
    },
    userId: {
      type: String,
      trim: true,
      required: true,
    },
    billingAddress: AddressSchema,
    shippingAddress: AddressSchema,
    shippingMethod: {
      type: String,
      default: null,
    },
    paymentMethod: {
      type: String,
      enum: PaymentMethodEnum,
      required: true,
    },
    orderedDate: {
      type: Date,
      default: () => new Date(),
    },
    orderStatus: {
      type: String,
      enum: OrderStatusEnum,
      default: OrderStatusEnum.Pending,
    },
    shippingStatus: {
      type: String,
      enum: ShippingStatusEnum,
      default: ShippingStatusEnum.PRE_TRANSIT,
    },
    paymentStatus: {
      type: String,
      enum: PaymentStatusEnum,
      default: PaymentStatusEnum.Pending,
    },
    products: [ProductSchema],
    productCost: {
      type: Number,
      required: true,
    },
    shippingCost: {
      type: Number,
      default: 0,
    },
    totalCost: {
      type: Number,
      required: true,
    },
    paymentId: {
      type: String,
      trim: true,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const OrderModel = model<OrderEntity>('order', OrderSchema);
export { OrderModel };
