import { model, Schema } from 'mongoose';
import { randomUUID } from 'crypto';
import { Review } from 'src/entity/review';

const ReviewSchema = new Schema<Review>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    productId: {
      type: String,
      required: true,
      index: true,
    },
    orderId: {
      type: String,
      required: true,
      index: true,
    },
    text: {
      type: String,
      default: null,
    },
    image: [
      {
        url: String,
        _id: false,
      },
    ],
    userId: {
      type: String,
      required: true,
    },
    rating: {
      type: Number,
      min: 1,
      max: 5,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ReviewModel = model<Review>('review', ReviewSchema);

export { ReviewModel };
