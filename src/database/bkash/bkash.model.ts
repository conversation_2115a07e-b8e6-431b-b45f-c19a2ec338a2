import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { BkashAgreementEntity, BkashPaymentEntity, BkashPayoutEntity, BkashRefundEntity, ModuleTypeEnum, PayoutTypeEnum, TransactionStatusEnum } from 'src/modules/bkash/entities/bkash.entity';

const BkashPaymentSchema = new Schema<BkashPaymentEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    // This could be subscriptionId or orderId 
    referenceId: {
      type: String,
    },
    userId: {
      type: String,
    },
    paymentId: {
      type: String,
      unique: false,
    },
    trxId: {
      type: String,
      unique: false,
      required: false,
    },
    transactionStatus: {
      type: String,
      enum: TransactionStatusEnum,
      required: true,
    },
    moduleType: {
      type: String,
      enum: ModuleTypeEnum,
      required: true,
    },
    invoiceNumber: {
      type: String,
      unique: false,
      required: false,
    },
    amount: {
      type: Number,
      required: true,
    },
    customerMsisdn: {
      type: String,
      required: false,
    },
    paymentCreateTime: {
      type: String,
      required: false,
    },
    paymentExecuteTime: {
      type: String,
      required: false,
    },
    // ID of BkashAgreementSchema
    bkashAgreementId: {
      type: String,
      required: false,
    }
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const BkashPaymentModel = model<BkashPaymentEntity>(
  'bkash-payment',
  BkashPaymentSchema,
);

const BkashAgreementSchema = new Schema<BkashAgreementEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
    },
    // Agreement ID from Bkash
    agreementId: {
      type: String,
      required: false,
    },
    // Status of the agreement
    agreementStatus: {
      type: String,
      enum: TransactionStatusEnum,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
)

const BkashAgreementModel = model<BkashAgreementEntity>(
  'bkash-agreement',
  BkashAgreementSchema,
);

const BkashPayoutSchema = new Schema<BkashPayoutEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
    },
    // This could be coachId or vendorId
    referenceId: {
      type: String,
      required: false,
    },
    // From bKash
    trxId: {
      type: String,
      unique: false,
      required: false,
    },
    // Status of the payout
    transactionStatus: {
      type: String,
      enum: TransactionStatusEnum,
      required: true,
    },
    invoiceNumber: {
      type: String,
      unique: true,
      required: false,
    },
    amount: {
      type: Number,
      required: true,
    },
    receiverMsisdn: {
      type: String,
      required: true,
    },
    payoutType: {
      type: String,
      enum: PayoutTypeEnum,
      default: PayoutTypeEnum.b2c,
      required: true,
    },
    payoutCompletedTime: {
      type: String,
      required: false,
    },
    moduleType: {
      type: String,
      enum: ModuleTypeEnum,
      required: true,
    }
  },
  {
    timestamps: true,
    versionKey: false,
  },
)

const BkashPayoutModel = model<BkashPayoutEntity>(
  'bkash-payout',
  BkashPayoutSchema,
);

const BkashRefundSchema = new Schema<BkashRefundEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
    },
    // This could be coach payment id or 
    referenceId: {
      type: String,
      required: false,
    },
    paymentId: {
      type: String,
      required: false,
    },
    // From bKash
    originalTrxId: {
      type: String,
      unique: true,
      required: false,
    },
    refundTrxId: {
      type: String,
      unique: false,
      required: false,
    },
    // Status of the refund
    refundTransactionStatus: {
      type: String,
      enum: TransactionStatusEnum,
      required: true,
    },
    invoiceNumber: {
      type: String,
      unique: false,
      required: false,
    },
    refundAmount: {
      type: Number,
      required: true,
    },
    
    refundCompletedTime: {
      type: String,
      required: false,
    },
    reason: {
      type: String,
      required: true,
    },
    moduleType: {
      type: String,
      enum: ModuleTypeEnum,
      required: true,
    }
  },
  {
    timestamps: true,
    versionKey: false,
  },
)

const BkashRefundModel = model<BkashRefundEntity>(
  'bkash-refund',
  BkashRefundSchema,
);

export { BkashAgreementModel, BkashPaymentModel, BkashPayoutModel, BkashRefundModel };
