// src/models/reel.model.ts
import { randomUUID } from 'crypto';
import { Schema, model, Document, Types } from 'mongoose';
import { Reels, ReelsPrivacy, ReelsType,  } from 'src/entity/reels';

export const ReelsMediaType = {
    type: [
      {
        url: { type: String, trim: true },
      },
    ],
    default: [],
    _id: false,
  };



// Define the main Reel schema
const ReelsSchema = new Schema<Reels>(
  {
    id: {
        type: String,
        default: () => randomUUID(),
        unique: true,
      },
    userId: {
        type: String,
        index: true,
        required: true,
    },
    type: {
      type: String,
      enum: ReelsType,
      default: ReelsType.OWN,
    },
    content: {
      type: String,
      trim: true,
      default: '',
    },
    videos: ReelsMediaType,
    tags: {
      type: [String],
      default: [],
    },
    privacy: {
      type: String,
      enum: ReelsPrivacy,
      default: ReelsPrivacy.PUBLIC,
      index: true,
    },
    totalReactions: {
      type: Number,
      default: 0,
    },
    totalComments: {
      type: Number,
      default: 0,
    },
    totalShares: {
      type: Number,
      default: 0,
    },
    location: {
      type: String,
      trim: true,
      default: '',
    },
    weight: {
      // Example weights: reaction = 5, comment = 10, share = 15
      type: Number,
      default: 0,
      select: false,
      index: true,
    },
  },
  {
    timestamps: true, // Automatically manages createdAt and updatedAt
    versionKey: false, // Disables the __v field
  }
);

ReelsSchema.add({ sharedReels: ReelsSchema });
const ReelsModel = model<Reels>('reels', ReelsSchema);
export { ReelsModel };
