// src/models/reel.model.ts
import { randomUUID } from 'crypto';
import { Schema, model, Document, Types } from 'mongoose';
import { Reels, ReelsPrivacy, ReelsType,  } from 'src/entity/reels';
import { ReelsMediaType } from './reels.model';

const PendingReelsSchema = new Schema<Partial<Reels>>(
  {
    id: {
        type: String,
        default: () => randomUUID(),
        unique: true,
      },
    userId: {
        type: String,
        index: true,
        required: true,
    },
    type: {
      type: String,
      enum: ReelsType,
      default: ReelsType.OWN,
    },
    content: {
      type: String,
      trim: true,
      default: '',
    },
    videos: ReelsMediaType,
    tags: {
      type: [String],
      default: [],
    },
    privacy: {
      type: String,
      enum: ReelsPrivacy,
      default: ReelsPrivacy.PUBLIC,
      index: true,
    },
 
    location: {
      type: String,
      trim: true,
      default: '',
    },
   
  },
  {
    timestamps: true, 
    versionKey: false, 
  }
);

PendingReelsSchema.add({ sharedReels: PendingReelsSchema });
const PendingReelsModel = model<Reels>('pending-reels', PendingReelsSchema);
export { PendingReelsModel };
