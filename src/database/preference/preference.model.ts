import { ProfileVisibilityEnum } from 'models';
import { model, Schema } from 'mongoose';
import { Preference, PrivacyPreference } from 'src/entity/preference';

const PrivacyPreferenceSchema = new Schema<PrivacyPreference>(
  {
    profileVisibility: {
      type: String,
      enum: ProfileVisibilityEnum,
    },
    notification: {
      email: <PERSON><PERSON><PERSON>,
      push: <PERSON><PERSON><PERSON>,
    },
  },
  {
    _id: false,
    timestamps: false,
    versionKey: false,
  },
);

const PreferenceSchema = new Schema<Preference>(
  {
    userId: {
      type: String,
      unique: true,
    },
    privacy: {
      type: PrivacyPreferenceSchema,
      default: {
        profileVisibility: ProfileVisibilityEnum.PUBLIC,
        notification: {
          email: true,
          push: true,
        },
      },
    },
  },
  {
    timestamps: false,
    versionKey: false,
  },
);

const PreferenceModel = model<Preference>('user-preference', PreferenceSchema);
export { PreferenceModel };
