import { randomUUID } from 'crypto';
import { NotificationModule } from 'models';
import { model, Schema } from 'mongoose';
import { NotificationRecipient } from 'src/entity/notificationRecipient';

const NotificationRecipientSchema = new Schema<NotificationRecipient>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    notificationId: {
      type: String,
      required: true,
    },
    module: {
      type: String,
      enum: NotificationModule,
    },
    seenAt: {
      type: Date,
      default: null,
    },
    targetUser: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

NotificationRecipientSchema.index({ targetUser: 1, notificationId: 1 });
const NotificationRecipientModel = model<NotificationRecipient>(
  'NotificationRecipient',
  NotificationRecipientSchema,
);
export { NotificationRecipientModel };
