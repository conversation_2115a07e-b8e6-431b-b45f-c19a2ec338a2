import { randomUUID } from 'crypto';
import { NotificationType } from 'models';
import { model, Schema } from 'mongoose';
import { Notification } from 'src/entity/notification';

const NotificationSchema = new Schema<Notification>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    title: {
      type: String,
      default: null,
    },
    content: {
      type: String,
      default: null,
    },
    type: {
      type: String,
      enum: NotificationType,
    },
    refId: String,
    documentId: String,
    createdBy: {
      id: String,
      name: String,
      avatar: {
        type: String,
        default: null,
      },
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

NotificationSchema.index({ 'createdBy.id': 1, module: 1, type: 1 });
const NotificationModel = model<Notification>(
  'notification',
  NotificationSchema,
);
export { NotificationModel };
