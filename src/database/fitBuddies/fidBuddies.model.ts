import { FitBuddiesStatus, FitBuddiesType } from 'models';
import { model, Schema } from 'mongoose';
import { FitBuddies } from 'src/entity/fitBuddies';

const FitBuddiesSchema = new Schema<FitBuddies>(
  {
    userId: {
      type: String,
      required: true,
    },
    fitBuddyId: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: FitBuddiesStatus,
      default: FitBuddiesStatus.ACTIVE,
    },
    type: {
      type: String,
      enum: FitBuddiesType,
      default: FitBuddiesType.FOLLOWER,
    },
    affinity: {
      type: [
        {
          date: {
            type: Date,
            default: new Date(),
          },
          score: Number, // like = 5, comment = 10, share = 15
        },
      ],
      index: true,
      _id: false,
    },
    blockedBy: String,
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

FitBuddiesSchema.index({ userId: 1, affinity: 1, fitBuddyId: 1 });
const FitBuddiesModel = model<FitBuddies>('FitBuddies', FitBuddiesSchema);
export { FitBuddiesModel };
