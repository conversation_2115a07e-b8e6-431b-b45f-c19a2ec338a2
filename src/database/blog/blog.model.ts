import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Blog, BlogCategory } from 'src/entity/blog';

const blogSchema = new Schema<Blog>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    authorId: {
      type: String,
    },
    title: {
      type: String,
      trim: true,
      default: null,
    },
    content: {
      type: String,
      trim: true,
      default: null,
    },
    category: {
      type: String,
      enum: BlogCategory,
      default: BlogCategory.GENERAL,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const blogModel = model<Blog>('blog', blogSchema);
export { blogModel };
