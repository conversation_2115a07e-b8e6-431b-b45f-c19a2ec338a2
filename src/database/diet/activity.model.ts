import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Activity } from 'src/entity/activity';

const activitySchema = new Schema<Activity>(
  {
    id: {
        type:String,
        default: () => randomUUID(),
    },
    name: {
        type: String,
        default: null,
    },
    activityType: {
        type: String,
        default: null,
    },
    description: {
        type: String,
        default: null,
    },
    value: {
        type: Number,
        default: null,
    },
},
{
  versionKey: false,
  timestamps: true,
},
);


const activityModel = model<Activity>(
  'activity',
  activitySchema,
);
export { activityModel };
