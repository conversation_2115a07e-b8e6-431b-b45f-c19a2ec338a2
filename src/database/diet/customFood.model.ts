import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { CustomFood } from 'src/entity/customFood';

const customFoodSchema = new Schema<CustomFood>(
  {
    id: {
        type:String,
        default: () => randomUUID(),
    },
    name: {
        type: String,
        default: null,
    },
    calories: {
        type: Number,
        default: null,
    },
    carb: {
        type: Number,
        default: null,
    },
    fat: {
        type: Number,
        default: null,
    },
    protein: {
        type: Number,
        default: null,
    },
    userId:{
        type:String,
    }
},
{
  versionKey: false,
  timestamps: true,
},
);


const customFoodModel = model<CustomFood>(
  'custom-food',
  customFoodSchema,
);
export { customFoodModel };
