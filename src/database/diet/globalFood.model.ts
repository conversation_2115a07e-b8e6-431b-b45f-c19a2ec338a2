import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { GlobalFood } from 'src/entity/globalFood';

const nutrientsSchema = new Schema({
  ENERC_KCAL: {
    type: Number,
    default: 0
  },
  PROCNT: {
    type: Number,
    default: 0
  },
  FAT: {
    type: Number,
    default: 0
  },
  CHOCDF: {
    type: Number,
    default: 0
  },
  FIBTG: {
    type: Number,
    default: 0
  }
}, { _id: false });

const measureSchema = new Schema({
  label: {
    type: String,
    required: true
  },
  weight: {
    type: Number,
    required: true
  },
  nutrients: {
    type: nutrientsSchema,
    required: true
  }
}, { _id: false });

const globalFoodSchema = new Schema<GlobalFood>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    brand: {
      type: String,
      default: null,
    },
    nutrients: {
      type: nutrientsSchema,
      required: true,
    },
    measures: {
      type: [measureSchema],
      default: [],
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);


const globalFoodModel = model<GlobalFood>(
  'global-food',
  globalFoodSchema,
);
export { globalFoodModel };
