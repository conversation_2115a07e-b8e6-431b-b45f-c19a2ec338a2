import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { PaymentMethodEnum, PaymentStatusEnum } from 'src/entity/order';
import { Payment } from 'src/entity/payment';

const PaymentSchema = new Schema<Payment>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
    },
    orderId: {
      type: String,
      unique: true,
    },
    sessionId: {
      type: String,
      default: () => randomUUID(),
    },
    paymentStatus: { type: String, enum: PaymentStatusEnum },
    paymentMethod: { type: String, enum: PaymentMethodEnum },
    totalCost: Number,
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const AmountPerPointSchema = new Schema(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    amount: {
      type: Number,
      default: 0,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const PaymenMethodListSchema = new Schema(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    list: {
      type: [String],
      enum: [PaymentMethodEnum],
      default: [],
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const AppleTrxIdSchema = new Schema(
  {
    userId: {
      type: String,
    },
    transactionId: {
      type: String,
    },
    productId: {
      type: String,
      default: '',
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const PaymentModel = model<Payment>('payment', PaymentSchema);
const AmountPerPointModel = model('amount-per-point', AmountPerPointSchema);
const PaymenMethodListModel = model('payment-methods', PaymenMethodListSchema);
const AppleTrxModel = model('apple-transaction', AppleTrxIdSchema);

export {
  PaymentModel,
  AmountPerPointModel,
  PaymenMethodListModel,
  AppleTrxModel,
};
