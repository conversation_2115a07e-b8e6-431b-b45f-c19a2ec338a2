import { randomUUID } from 'crypto';
import { PackagePaymentTypeEnum } from 'models';
import { model, Schema } from 'mongoose';
import { Subscription } from 'src/entity/subscription';

const SubscriptionSchema = new Schema<Subscription>(
  {
    id: { type: String, default: () => randomUUID(), unique: true },
    userId: { type: String },
    packageId: { type: String },
    productIdName: { type: String },
    isActive: { type: Boolean, default: false },
    price: { type: Number },
    isPaid: { type: Boolean, default: false },
    paymentType: { type: String, enum: PackagePaymentTypeEnum },
    paidAt: { type: Date },
    expireAt: { type: Date, index: true },
  },
  { timestamps: true, versionKey: false },
);
const SubscriptionModel = model<Subscription>(
  'subscriptions',
  SubscriptionSchema,
);

export { SubscriptionModel };
