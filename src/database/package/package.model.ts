import { randomUUID } from 'crypto';
import { DurationUnitEnum } from 'models';
import { model, Schema } from 'mongoose';
import { SubscriptionPackage } from 'src/entity/package';

// Define the schema
const PackageSchema = new Schema<SubscriptionPackage>(
  {
    id: { type: String, default: () => randomUUID(), unique: true },
    name: { type: String },
    type: { type: String },
    title: { type: String },
    description: { type: String },
    price: { type: Number },
    duration: { type: Number },
    durationInDays: { type: Number },
    durationUnit: { type: String, enum: DurationUnitEnum },
    currency: { type: String },
    currencySymbol: { type: String },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Define the model
const SubscriptionPackageModel = model<SubscriptionPackage>(
  'packages',
  PackageSchema,
);
export { SubscriptionPackageModel };
