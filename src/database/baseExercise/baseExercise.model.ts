import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { BaseExcercise } from 'src/entity/baseExercise';

const BaseExercise = new Schema<BaseExcercise>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    name: {
      type: String,
      unique: true,
    },
    description: {
      type: String,
      default: '',
    },
    mechanics: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    category: {
      type: [String],
      default: null,
    },
    forceType: {
      type: [String],
      default: null,
    },
    primaryMuscles: {
      type: [String],
      default: [],
    },
    secondaryMuscles: {
      type: [String],
      default: [],
    },
    equipments: {
      type: [String],
      default: [],
    },
    preview: {
      type: String,
      default: null,
    },
  },
  {
    versionKey: false,
  },
);

const BaseExerciseModel = model<BaseExcercise>('base-exercise', BaseExercise);

export { BaseExerciseModel };
