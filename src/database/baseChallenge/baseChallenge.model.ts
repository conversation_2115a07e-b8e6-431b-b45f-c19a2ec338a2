import { randomUUID } from 'crypto';
import { IChallengeDifficultyEnum } from 'models';
import { model, Schema } from 'mongoose';
import { BaseChallenge } from 'src/entity/baseChallenge';

const BaseChallengeSchema = new Schema<BaseChallenge>(
  {
    id: { type: String, unique: true, default: () => randomUUID() },

    // base exercise properties
    name: {
      type: String,
    },
    description: {
      type: String,
      default: '',
    },
    mechanics: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    category: {
      type: [String],
      default: null,
    },
    forceType: {
      type: [String],
      default: null,
    },
    primaryMuscles: {
      type: [String],
      default: [],
    },
    secondaryMuscles: {
      type: [String],
      default: [],
    },
    equipments: {
      type: [String],
      default: [],
    },
    preview: {
      type: String,
      default: null,
    },

    // additionl properties
    difficulty: {
      type: String,
      enum: IChallengeDifficultyEnum,
      default: IChallengeDifficultyEnum.BEGINNER,
    },
    duration: { type: Number, default: 0 },
    loop: { type: Number, default: 0 },
    points: { type: Number, default: 0 },
    startDate: { type: Date, default: null },
    endDate: { type: Date, default: null },
    totalPick: { type: Number, default: 0 },
    isActive: { type: Boolean, default: true },
  },
  {
    versionKey: false,
  },
);

const BaseChallengeModel = model<BaseChallenge>(
  'base-challenge',
  BaseChallengeSchema,
);
export { BaseChallengeModel };
