import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { ClubMember } from 'src/entity/club-member';

const ClubMemberSchema = new Schema<ClubMember>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
      required: true,
    },
    userId: {
      type: String,
      required: true,
    },
    userName: String,
    clubId: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ['PENDING', 'ACTIVE', 'EXPIRED', 'BLOCKED'],
      required: true,
    },
    expireDate: Date,
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ClubMemberModel = model<ClubMember>('clubMember', ClubMemberSchema);
export { ClubMemberModel };
