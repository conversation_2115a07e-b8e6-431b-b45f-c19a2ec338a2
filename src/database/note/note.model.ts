import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Note } from 'src/entity/note';

const NoteSchema = new Schema<Note>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      index: true,
      required: true,
    },
    title: {
      type: String,
      trim: true,
      required: true,
    },
    content: {
      type: String,
      trim: true,
      required: true,
    },
    date: {
      type: String,
      index: true,
      required: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);
const NoteModel = model<Note>('note', NoteSchema);
export { NoteModel };
