export const enum TrainingErrorEnum {
  CAN_NOT_CREATE_TRAINING_EXERCISE = 'Can not create training exercise',
  PROPERTY_OF_INTERMEDIATE_REQUIRED = 'Property of Intermediate Required',
  PROPERTY_OF_ADVANCE_REQUIRED = 'Property of advance required',
  FORCETYPE_REQUIRED = 'forceType required',
  CAN_NOT_GET_TRAINING_EXERCISE = 'Can not get training exercise',
  CAN_NOT_GET_WEEKLY_EXERCISE_LIST = 'Can not get weekly exercise list',
  CAN_NOT_UPDATE_TRAINING = 'Can not update training',
  CAN_NOT_DELETE_TRAINING = 'Can not delete training',
  CAN_NOT_GET_INTERMEDIATE_TRAINING = 'Can not get intermediate training',
  PROVIDE_WEEK_AND_DAY = 'Provide week and day',
  PROVIDE_BODY_BUILDING_PROGRAM_ID = 'Provide body building program id(programId)',
}
