export const enum ChatErrorEnum {
  FAILED_TO_UPDATE_CONVERSATION = 'Failed to update conversation',
  FAILED_TO_ADD_CONVERSATION = 'Failed to add conversation',
  FAILED_TO_ADD_MEMBER = 'Failed to add member',
  YOU_ARE_NOT_ALLOWED_TO_ADD_MEMBER = 'You are nit allowed to add member',
  THIS_MEMBER_IS_NOT_YOUR_FITBUDDY = 'This member is not your fitbuddy',
  INVALID_REQUEST = 'Invalid request',
  MESSAGE_NOT_SENT = 'Message not sent',
  FAILED_TO_FETCH_CHAT_HISTORY = 'Failed to fetch chat history',
  MESSAGE_CAN_NOT_BE_DELETED = 'Message can not be deleted',
  MAXIMUM_NUMBER_OF_USER_REACHED = 'Maximum number of user reached',
}
