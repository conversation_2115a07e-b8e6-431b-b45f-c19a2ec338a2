import { SuccessResponse } from 'models/common';

/**
 * API Path: /api/clubs/{clubId}/leave-and-join-club
 * method: PATCH
 * Param: LeaveAndJoinClubParam
 * response: LeaveAndJoinClubResponse
 */
export interface LeaveAndJoinClubParam {
  clubId: string;
}

export const enum LeaveAndJoinClubSuccessMessage {
  SUCCESSFULLY_LEAVE_PREVIOUS_CLUB_AND_JOINED_TO_ANOTHER_CLUB = 'Successfully leave previous club and joined to another club',
}

export interface LeaveAndJoinClubResponse extends SuccessResponse {
  data: {
    message?: LeaveAndJoinClubSuccessMessage;
  };
}

export const enum LeaveAndJoinClubErrorMessages {
  ERROR_FETCHING_TO_LEAVE_CLUB_AND_JOIN_TO_ANOTHER_CLUB = 'Unable to join the club at this moment. Please try again later',
  ALREADY_YOU_ARE_A_MEMBER_OF_THIS_CLUB = 'Already you are a member of this club',
  NO_CLUB_EXIST = 'no club exist',
}
