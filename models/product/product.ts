export interface ProductInfo {
  name: string;
  shortDescription?: string;
  fullDescription?: string;
  sku: string;
  price: number;
  oldPrice: number;
  showOnHomePage?: boolean;
  includeInTopMenu?: boolean;
  allowToSelectPageSize?: boolean;
  published?: boolean;
  displayOrder?: number;
  isFeatured?: boolean;
  publishDate?: Date;
  size?: string[];
  color?: string[];
  stock?: number;
  length?: number;
  height?: number;
  width?: number;
  weight?: number;
  weightUnit?: string;
}

export interface ProductMeta {
  keywords?: string[];
  title?: string;
  description?: string;
  friendlyPageName?: string;
}

export interface ProductPhoto {
  url?: string;
  id?: string;
  title?: string;
  color?: string;
  alt?: string;
  displayOrder?: number;
}

export interface ProductCategory {
  id: string;
  name: string;
}

export interface ProductManufacturer {
  id: string;
  name: string;
}

export interface Product {
  id?: string;
  info: ProductInfo;
  meta?: ProductMeta;
  tags?: string[];
  photos?: ProductPhoto[];
  brands?: string[];
  manufacturer?: ProductManufacturer;
  categories: ProductCategory[];
  avgRating: number;
}
