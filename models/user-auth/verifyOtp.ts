import { SuccessResponse } from '../common/index';

/**
 * API Path: /user-auth/forgot-password/verify-otp
 * method: POST
 * body: VerifyOtpRequest
 * response: VerifyOtpResponse
 */

export interface VerifyOtpRequest {
  email?: string;
  phone?: string;
  otp: number;
}

export declare const enum VerifyOtpSuccessMessages {
  OTP_VERIFIED_SUCCESSFUL = 'Otp verification successful',
}

export interface VerifyOtpSuccessResponse extends SuccessResponse {
  data: {
    message?: VerifyOtpSuccessMessages;
  };
}

export declare const enum VerifyOtpErrorMessages {
  USER_PHONE_ALREADY_EXITS = 'User phone already exists',
  USER_EMAIL_ALREADY_EXITS = 'User email already exists',
  OTP_EXPIRED_OR_INVALID_OTP = 'Otp expire or invalid otp',
}
