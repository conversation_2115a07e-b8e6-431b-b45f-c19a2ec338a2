import { SuccessResponse } from '../common/index';

/**
 * API Path: /user-auth/forgot-password
 * method: POST
 * body: UserForgotPasswordRequest
 * response: UserForgotPasswordResponse
 */

export interface UserForgotPasswordRequest {
  email?: string;
  phone?: string;
  password: string;
}

export interface UserForgotPasswordMessage {
  message?: string;
}

export interface UserForgotPasswordSuccessResponse extends SuccessResponse {
  data: UserForgotPasswordMessage;
}

export const enum UserForgotPasswordSuccessMessages {
  FORGOT_PASSWORD_SUCCESSFUL = 'Forgot password successful',
}

export const enum UserForgotPasswordErrorMessages {
  CAN_NOT_GET_USER = 'Can not get user',
  CAN_NOT_UPDATE_USER_PASSWORD = 'Can not update user password',
}
