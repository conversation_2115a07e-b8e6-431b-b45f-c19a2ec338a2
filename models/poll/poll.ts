
export interface PollTeam {
  name: string;
  url?:string;
  votes?:number;
  isVoted?: boolean
}

export interface PollPlayer {
  name: string;
  team?: string;
  url?:string;
  votes?:number;
  isVoted?: boolean
}


export interface AddPlayer {
  name: string; 
  url?:string;
  
}
export interface Poll{
  pollType: PollType;
  title: string;
  subTitle?:string;
  status?: PollStatus;
  options?: PollOption[];
  description?: string;
  image?: string;
  expirationDate?:Date;
  joinedUsers?: string[];

}

export interface PollOption {
  id?: string;
  title: string;
  vote?: number;
  description?: string;
  image?: string; 
}


export enum PollType {
  GENERAL="GENERAL",
  TEAM="TEAM",
  PLAYER='PLAYER'
}

export enum AllPollType {
  ALL="ALL",
  GENERAL="GENERAL",
  TEAM="TEAM",
  PLAYER='PLAYER'
}

export enum PollStatus{
  CREATED='CREATED',
  ACTIVE='ACTIVE',
  INACTIVE='INACTIVE',
  CLOSED='CLOSED',
  DELETED='DELETED'

}


