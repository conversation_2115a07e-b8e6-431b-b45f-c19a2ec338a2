
/**
 * API Path: /api/activities
 * method: GET
 * response: GetAllActivitiesResponse
 */

import { SuccessResponse } from "models/common";
import { AllPollType, Poll, PollType } from "./poll";

export interface GetPollQuery {
  pollType?:AllPollType;
  offset?: number;
  limit?: number;
 
}

export interface GetAllPollSuccessResponse extends SuccessResponse {
  data: Poll[];
}


export type GetPollListWithUsersAndVoteInfo = Poll &{ 
  users: string[];
  myStatus:string;
  totalVotes:number;
}

export const enum GetAllPollErrorMessages {
  CAN_NOT_GET_ALL_POLL= 'Can not get all poll',
}
