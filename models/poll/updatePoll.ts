
import { SuccessResponse } from "models/common";
import { PollOption, PollStatus, PollType } from "./poll";
import { Poll } from "src/entity/poll";

export interface UpdatePollRequestBody{
    pollType?: PollType;
    title?: string;
    subTitle?:string;
    status?: PollStatus;
    options?: PollOption[];
    description?: string;
    image?: string;
    expirationDate?:Date;
  
  
}

export interface UpdateExpirationDatePollRequestBody {
  expirationDate:Date;
}

export interface UpdateStatusPollRequestBody {
  status?: PollStatus;
}

export interface UpdatePollParams {
    pollId: string;
  }


export interface CreateWinnerList{
  pollId: string;
  winnerCount:number;
  pollOptionId:string;
}


export interface UpdatePollSuccessResponse extends SuccessResponse{
    data: Poll
}

export type UpdatePollResponse =UpdatePollSuccessResponse;


export const enum UpdatePollErrorMessages {
    
    CAN_NOT_UPDATE_POLL = 'Can not update poll',
  }


