
import { SuccessResponse } from "models/common";
import { PollOption, PollStatus, PollType } from "./poll";
import { Poll } from "src/entity/poll";

export interface CreatePollRequestBody{
    pollType: PollType;
    title: string;
    subTitle?:string;
    status?: PollStatus;
    options?: PollOption[];
    description?: string;
    image?: string;
    expirationDate?:Date;
    isPinned?:boolean
  
  
}


export interface CreatePollSuccessResponse extends SuccessResponse{
    data: Poll
}

export type CreatePollResponse =CreatePollSuccessResponse;


