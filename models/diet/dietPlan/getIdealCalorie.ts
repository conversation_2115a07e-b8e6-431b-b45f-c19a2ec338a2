import { SuccessResponse } from '../../common/index';
import { IdealCalorie } from './idealCalorie';

/**
 * API Path: /api/diet/ideal-calories/:dietId
 * method: GET
 * response: GetDietStatusResponse
 */
export interface GetIdealCalorieSuccessResponse extends SuccessResponse {
  data: IdealCalorie;
}

export const enum GetIdealCalorieErrorMessages {
  CAN_NOT_GET_DIET= 'Can not find dietplan',
  CAN_NOT_GET_ACTIVITY= 'Can not find activity level',
}
