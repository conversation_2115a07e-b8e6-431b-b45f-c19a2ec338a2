export enum GenderEnum {
    MALE='male',
    FEMALE='female'
}
export enum WeightEnum {
    LB='lb',
    KG='kg'
}
export enum heightEnum {
    IN='in',
    CM='cm',
    FT='ft'
    

}
  

export interface UserInfo {
  id?: string;
  age: number;
  weight: number;
  targetWeight: number;
  weightType: string;
  height: number;
  heightType: string;
  gender: string;
  activityId: string;
  weeklyGoal: string;
}
