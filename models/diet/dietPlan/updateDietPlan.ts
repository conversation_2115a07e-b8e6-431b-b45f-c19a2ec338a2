import { SuccessResponse } from '../../common/successResponse';
import { DietPlan } from './dietPlan';

export interface UpdatedUserInfo {
  id?: string;
  age?: number;
  weight?: number;
  targetWeight?: number;
  weightType: string;
  height?: number;
  heightType: string;
  gender?: string;
  activityId?: string;
  weeklyGoal?: string;
}


export interface UpdateDietPlanRequestBody {
    user?: UpdatedUserInfo;
    type?: string;
    startDate?: Date;
    endDate?: Date;
}

export interface UpdateDietPlanResponse extends SuccessResponse {
  data: DietPlan;
}

export const enum UpdateDietPlanErrorMessage {
  CAN_NOT_UPDATE_DIET_PLAN = 'Can not update diet plan',
  DIET_PLAN_NOT_FOUND = 'diet plan not existed',
}
