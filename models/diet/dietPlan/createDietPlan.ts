import { SuccessResponse } from '../../common/successResponse';
import { DietPlan } from './dietPlan';
import { UserInfo } from './userInfo';

export interface CreateDietPlanRequestBody {
    user: UserInfo;
    type: string;
    startDate: Date;
    endDate: Date;
}

export interface CreateDietPlanResponse extends SuccessResponse {
  data: DietPlan;
}

export const enum CreateDietPlanErrorMessage {
  CAN_NOT_CREATE_DIET_PLAN = 'Can not create diet plan',
  DIET_PLAN_EXISTS = 'diet plan already exists',
}
