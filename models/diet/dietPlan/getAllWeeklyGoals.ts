import { SuccessResponse } from '../../common/index';
import { WeeklyGoal } from './weeklyGoal';
/**
 * API Path: /api/weekly-goals
 * method: GET
 * response: GelAllWeeklyGoalsResponse
 */

export interface GelAllWeeklyGoalsSuccessResponse extends SuccessResponse {
  data: WeeklyGoal[];
}

export const enum GelAllWeeklyGoalsErrorMessages {
  CAN_NOT_GET_ALL_WEEKLY_GOALS= 'Can not get all weekly-goals',
}
