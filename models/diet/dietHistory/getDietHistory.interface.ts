import { SuccessResponse } from '../../common/index';
import { DietHistory, DietHistoryResponseBody } from './dietHistory';
/**
 * API Path: /api/diet/diet-history/:userId/:date
 * method: GET
 * response: GetAllDietHistoryResponse
 */


export interface GetDietHistoryQuery {
  date: Date;
}



export interface GetAllDietHistorySuccessResponse extends SuccessResponse {
  data: DietHistoryResponseBody;
}

export const enum GetAllDietHistoryErrorMessages {
  CAN_NOT_GET_DIET_HISTORY= 'Can not get diet history',
}
