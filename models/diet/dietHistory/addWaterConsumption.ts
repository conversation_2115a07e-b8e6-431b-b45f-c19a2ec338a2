import { SuccessResponse } from '../../common/successResponse';
import { DietHistoryResponseBody } from './dietHistory';
import { WaterConsumption } from './waterConsumption.interface';

export interface CreateWaterConsumptionRequestBody {
    userId?: string;
    date: Date;
    waterAmount: number;
}


export interface CreateWaterConsumptionSuccessResponse extends SuccessResponse {
  data: DietHistoryResponseBody;
}

export const enum CreateWaterConsumptionErrorMessage {
  CAN_NOT_ADD_WATER= 'Can not add water',
}
