import { DietHistory } from 'src/entity/dietHistory';
import { SuccessResponse } from '../../common/index';

/**
 * API Path: /api/diet/diet-history/delete-food
 * method: PUT
 * response: DeleteFoodFromHistoryResponse
 */

export interface DeleteFoodFromHistoryQuery {
    dietHistoryId: string;
    consumedFoodId: string;
  }

export interface DeleteFoodFromHistorySuccessResponse extends SuccessResponse {
  data: DietHistory;
}

export const enum DeleteFoodFromHistoryErrorMessages {
  CAN_NOT_DELETE_CONSUMED_FOOD = 'Can not delete consumed food',
  CAN_NOT_FIND_CONSUMED_FOOD= 'Can not find consumed food'
}
