import { SuccessResponse } from '../../common/successResponse';
import { CustomFood  } from './customFood';

export interface CreateCustomFoodRequestBody {
    name: string;
    calories: number;
    fat: number;
    carb: number;
    protein: number;
    userId?:string;
}

export interface CreateCustomFoodResponse extends SuccessResponse {
  data: CustomFood;
}

export const enum CreateCustomFoodErrorMessage {
  CAN_NOT_CREATE_CUSTOM_FOOD = 'Can not create custom food',
  CUSTOM_FOOD_EXISTS = 'Custom food already exists',
}
