import { SuccessResponse } from '../../common/index';
import { CustomFood } from './customFood';

/**
 * API Path: /api/diet/custom-foods
 * method: GET
 * response: GetAllCustomFoodsResponse
 */
export interface GetAllCustomFoodsQuery {
  offset?: number;
  limit?: number;
  search?: string;
}
export interface GetAllCustomFoodsSuccessResponse extends SuccessResponse {
  data: CustomFood[];
}

export const enum GetAllCustomFoodsErrorMessages {
  CAN_NOT_GET_ALL_CUSTOM_FOODS= 'Can not get all custom foods',
}
