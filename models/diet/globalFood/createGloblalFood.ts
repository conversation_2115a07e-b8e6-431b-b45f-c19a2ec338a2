import { SuccessResponse } from '../../common/successResponse';
import { GlobalFood, Measure, Nutrients } from './globalFood';


export interface CreateGlobalFoodRequestBody {
  name: string;
  brand?: string;
  nutrients: Nutrients;
  measures: Measure[];
}

export interface CreateGlobalFoodResponse extends SuccessResponse {
  data: GlobalFood;
}

export const enum CreateGlobalFoodErrorMessage {
  CAN_NOT_CREATE_GLOBAL_FOOD = 'Can not create global food',
  GLOBAL_FOOD_EXISTS = 'Global food already exists',
}
