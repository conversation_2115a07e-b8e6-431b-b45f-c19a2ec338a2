import { SuccessResponse } from '../../common/index';
import { GlobalFood } from './globalFood';
// import { CustomFood } from './customFood';

/**
 * API Path: /api/diet/custom-foods
 * method: GET
 * response: GetAllCustomFoodsResponse
 */
export interface GetAllGlobalCustomFoodsQuery {
  offset?: number;
  limit?: number;
  search?: string;
}
export interface GetAllGlobalCustomFoodsSuccessResponse extends SuccessResponse {
  data: GlobalFood[];
}

export const enum GetAllGlobalCustomFoodsErrorMessages {
  CAN_NOT_GET_ALL_GLOBAL_FOODS= 'Can not get all global foods',
}
