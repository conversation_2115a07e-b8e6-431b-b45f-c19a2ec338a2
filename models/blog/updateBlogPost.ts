import { SuccessResponse } from '../common/successResponse';
import { Blog, BlogCategory } from './blog';

export interface UpdateBlogPostRequestBody {
  title?: string;
  content?: string;
  category?: BlogCategory;
}

export interface UpdateBlogPostResponse extends SuccessResponse {
  data: Blog;
}

export const enum UpdateBlogPostErrorMessage {
  CAN_NOT_UPDATE_BLOG_POST = 'Can not update blog post',
  CAN_NOT_FIND_BLOG = 'Can not find blog',
  NO_PERMISSION = 'You do not have permission to edit this blog post',
}
