import { SuccessResponse } from '../common/successResponse';

export const enum BlogMessage {
  DELETED_BLOG_POST_SUCCESSFULLY = 'Deleted blog post successfully',
}

export interface DeleteBlogMessage {
  message: BlogMessage;
}

export interface DeleteBlogSuccessResponse extends SuccessResponse {
  data: DeleteBlogMessage;
}

export const enum DeleteBlogErrorMessage {
  CAN_NOT_DELETE_BLOG_POST = 'Can not delete blog post',
  CAN_NOT_FIND_BLOG = 'Can not find blog',
  NO_PERMISSION = 'You do not have permission to delete this blog post',
}
