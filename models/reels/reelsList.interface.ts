
import { ProductPhoto } from 'src/entity/product';
import { Reels } from 'src/entity/reels';

type userInfo = {
  name: string;
  image?: {
    profile: string;
  };
};

type productInfo = {
  name: string;
  price: number;
  oldPrice: number;
  photos: ProductPhoto[];
  avgRating: number;
};

export type GetReelsListWithUserInfoAndProductInfo = Omit<Reels, 'weight'> & {
  userInfo: userInfo;
  comments: {
    userId: string;
    comment: string;
    createdAt: Date;
    userInfo: userInfo;
  }[];
  productInfo?: productInfo;
};

export type GetReelsTimelineListWithUserInfo = Omit<Reels, 'weight'> & {
  liked: true;
  userInfo: userInfo;
  comments: {
    userId: string;
    comment: string;
    createdAt: Date;
    userInfo: userInfo;
  }[];
};

export type GetReelsListWithUserInfo = Omit<Reels, 'weight'> & {
  userInfo: userInfo;
  likersInfo:{
    userId:string;
    name: string;
    image:{
      profile: string;
    };
  }[];
  comments: {
    userId: string;
    comment: string;
    createdAt: Date;
    userInfo: userInfo;
  }[];
};

export type GetReelsListResponse = {
  data: GetReelsListWithUserInfoAndProductInfo[];
};

export type GetReelsTimelineListResponse = {
  data: GetReelsTimelineListWithUserInfo[];
};
export type GetSingleReelsResponse = { data: GetReelsListWithUserInfo };
