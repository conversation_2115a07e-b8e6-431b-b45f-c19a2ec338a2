export const enum ReelsErrorEnum {
  PROVIDE_NECESSARY_CONTENT = 'Provide necessary content',
  ERROR_IN_CREATING_NEW_REELS = 'Error in creating new Reels',
  REELS_NOT_FOUND = 'Reels not found',
  REELS_CAN_NOT_BE_UPDATED = 'Reels can not be updated',
  REELS_CAN_NOT_BE_DELETED = 'Reels can not be deleted',
  REELS_LIST_NOT_FOUND = 'Reels list not found',
  ERROR_IN_CREATING_NEW_REELS_COMMENT = 'Error in creating new reels comment',
  CAN_NOT_DO_THAT = 'Can not do that',
  ERROR_IN_SHARING_REELS = 'Error in sharing reels',
  CAN_NOT_SEE_REELS = 'Can not see reels',
  CAN_NOT_GET_USER_PREFERENCE = 'User not found or can not get user preference',
  USER_PROFILE_IS_LOCKED = 'User profile is locked',
}
