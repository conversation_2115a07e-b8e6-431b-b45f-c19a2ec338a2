import { SuccessResponse } from '../common/index';

/**
 * API Path: /api/posts/{id}
 * method: DELETE
 * params: DeleteClubParamsBody
 * response: DeleteClubResponse
 */


export const enum DeletePostSuccessMessages {
  POST_DELETED_SUCCESSFULLY = 'Post deleted successfully',
}

export const enum DeletePostErrorMessages {
  CAN_NOT_DELETE_POST = 'Can not delete post ',
  NO_POST_EXIST = 'No post exist',
}

export interface DeletePostSuccessResponse extends SuccessResponse {
  data: {
    message?: DeletePostSuccessMessages;
  };
}
