import { User } from 'models/user';

export enum NotificationModule {
  ALL = 'ALL',
  SPOT = 'SPOT',
  POST = 'POST',
  REELS = 'REELS',
  STORY = 'STORY',
  CHALLENGE = 'CHALLENGE',
  ORDER = 'ORDER',
  DIET = 'DIET',
  ONE_TO_ONE_CHAT = 'ONE_TO_ONE_CHAT',
  GROUP_CHAT = 'GROUP_CHAT',
  REMINDER = 'REMINDER',
}

export enum NotificationType {
  NEW_POST = 'New post',
  GROUP_REQUEST = 'Group request',
  GROUP_POST_REQUEST = 'Group post request',
  GROUP_POST = 'Group post',
  SPOT_REQUEST = 'Spot request',
  SPOT_REQUEST_ACCEPT = 'Spot request accept',
  POST_REACTION = 'Post reaction',
  POST_COMMENT = 'Post comment',
  REELS_REACTION = 'Reels reaction',
  REELS_COMMENT = 'Reels comment',
  STORY_VIEW = 'Story view',
  CHALLENGE_STATUS = 'Challenge status update',
  ORDER_STATUS = 'Order status update',
  DIET_ENDING = 'Diet ending soon',
  CHALLENGE_ENDING = 'Challenge ending soon',
  ONE_TO_ONE_CHAT = 'ONE_TO_ONE_CHAT',
  GROUP_CHAT = 'GROUP_CHAT',
  DAILY_REMINDER = 'Daily reminder',
}

export interface Notification {
  id: string;
  title: string;
  content: string;
  type: NotificationType;
  // refId means notification redirect Id -> If the notification module is post,/
  // then this notification will redirect post/postId.
  refId: string;
  // Notification creator
  createdBy: {
    userId: string;
    name: string;
    avatar: string;
  };
}

export interface NotificationRecipient {
  id: string;
  notificationId: string;
  module: NotificationModule;
  seenAt?: Date;
  targetUser: string;
  notification?: Notification;
}

export interface NotificationUser {
  id?: string;
  userId?: string;
  name: string;
  image?: {
    profile: string;
  };
  fcmToken?: string;
}

export enum NotificationCategory {
  STORY_VIEW = 'STORY_VIEW',
  POST_COMMENT = 'POST_COMMENT',
  REELS_COMMENT = 'REELS_COMMENT',
  POST_REACTION = 'POST_REACTION',
  REELS_REACTION = 'REELS_REACTION',
  SPOT_REQUEST_ACCEPT = 'SPOT_REQUEST_ACCEPT',
  SPOT_REQUEST = 'SPOT_REQUEST',
  DIET_ENDING = 'DIET_ENDING',
  CHALLENGE_ENDING = 'CHALLENGE_ENDING',
  CHALLENGE_STATUS = 'CHALLENGE_STATUS',
  ORDER_STATUS = 'ORDER_STATUS',
  ONE_TO_ONE_CHAT = 'ONE_TO_ONE_CHAT',
  GROUP_CHAT = 'GROUP_CHAT',
  DAILY_REMINDER = 'DAILY_REMINDER',
}
export interface NotificationPayload {
  title: string;
  content: string;
  createdBy: NotificationUser;
  recipient: NotificationUser;
  module: NotificationModule;
  type: NotificationType;
  date?: Date;
  category: NotificationCategory;
  task?: string;
  documentId: string;
  body?: string;
  data?: {
    [key: string]: string;
  };
  topic?: string;
}
