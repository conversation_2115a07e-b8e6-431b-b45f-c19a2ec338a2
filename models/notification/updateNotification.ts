import { SuccessResponse } from '../common/index';
import { NotificationRecipient } from './notification';

/**
 * API Path: /notifications
 * method: PATCH
 * body: UpdateNotificationsRequest
 * response: UpdateNotificationsResponse
 */

export interface UpdateNotificationsRequest {
  notificationIds: string[];
}

export const enum UpdateNotificationsErrorMessages {
  UPDATED_FAILED = 'Update failed',
}

export interface UpdateNotificationsSuccessResponse extends SuccessResponse {
  data: NotificationRecipient[];
}
