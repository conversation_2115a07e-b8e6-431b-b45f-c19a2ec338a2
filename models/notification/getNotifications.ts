import { SuccessResponse } from '../common/index';
import { NotificationRecipient } from './notification';

/**
 * API Path: /notifications
 * method: GET
 * query: GetNotificationsQuery
 * response: GetNotificationsResponse
 */

export interface GetNotificationsQuery {
  offset?: number;
  limit?: number;
  module?: string;
}

export interface GetNotificationsSuccessResponse extends SuccessResponse {
  data: NotificationRecipient[];
}

export const enum GetNotificationsErrorMessages {
  NO_NOTIFICATIONS_FOUND = 'No notifications found',
}
