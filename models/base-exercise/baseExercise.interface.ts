export interface ICreateBaseExcercise {
  id?: string;
  name: string;
  description?: string;
  mechanics?: string;
  type: string;
  category: string[];
  forceType?: string[];
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview: string;
}

export interface ICreateBaseExcerciseReq {
  id?: string;
  name: string;
  description?: string;
  mechanics?: string;
  type: string;
  category: string[];
  forceType?: string[];
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview: string;
}

export interface IUpdateBaseExcerciseReq {
  description?: string;
  mechanics?: string;
  type?: string;
  category?: string[];
  forceType?: string[];
  primaryMuscles?: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview?: string;
}

export interface ICreateBaseExcerciseRes {
  id: string;
  name: string;
  description: string;
  mechanics: string;
  type: string;
  category: string[];
  forceType: string[];
  primaryMuscles: string[];
  secondaryMuscles: string[];
  equipments: string[];
  preview: string;
  previewKey?: string;
}
