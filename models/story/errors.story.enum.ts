export const enum StoryErrorEnum {
  NO_FILE_IS_FOUND = 'No file is found',
  INVALID_FILE_TYPE = 'Invalid file type',
  ERROR_IN_UPLOADING_STORY = 'Error in uploading story',
  ERROR_IN_CREATING_NEW_STORY = 'Error in creating new story',
  STORY_CAN_NOT_BE_DELETED = 'Story can not be deleted',
  ERROR_IN_STORY_VIEW = 'Error in story view',
  ERROR_IN_GETTING_STORY_VIEWERS = 'Error in getting story viewers',
  ERROR_IN_GETTING_FIT_BUDDIES = 'Error in getting fit buddies',
  INVALID_REQUEST = 'Invalid request',
  INVALID_STORY = 'Invalid story',
}
