import { SuccessResponse } from '../common/index';
import { SpotProfile, SpotProfileImage } from './spotProfile';

/**
 * API Path: /spot/profile
 * method: POST
 * body: SpotProfileBody
 * response: SpotProfileResponse
 */

export interface CreateSpotProfileRequestBody {
  bio?: string;
 
  profession?: {
    company?: string;
    position?: string;
  };
  gender: string;
  workoutType?: string;
  bodyType?: string;
  clubId?: string;
  interest?: string[];
  education?: {
    school?: string;
    college?: string;
    university?: string;
  };
  images?: SpotProfileImage[];
  maxBench?: number;
  maxSquat: number;
  weightType?: string;
}

export interface CreateSpotProfileSuccessResponse extends SuccessResponse {
  data: SpotProfile;
}

export const enum CreateSpotProfileErrorMessages {
  PROFILE_ALREADY_EXISTED = 'profile already existed',
  USER_NOT_FOUND = 'user not found',
}
