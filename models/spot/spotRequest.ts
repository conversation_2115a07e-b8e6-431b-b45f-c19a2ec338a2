import { SuccessResponse } from '../common/index';
import { Spotted } from './spot';

/**
 * API Path: /spot/request
 * method: POST
 * body: SpotRequestBody
 * response: SpotRequestResponse
 */

export interface SpotRequestBody {
  recipientId: string;
}

export interface SpotRequestSuccessResponse extends SuccessResponse {
  data: Spotted;
}

export const enum SpotRequestErrorMessages {
  SPOT_REQUEST_FAILED = 'Spot request failed',
  RECIPIENT_NOT_FOUND = 'Recipient not found',
  ALREADY_SPOTTED = 'Already spotted',
  ALREADY_SEND_SPOT_REQUEST = 'Already send spot request',
  USER_ALREADY_SENT_YOU_SPOT_REQUEST = 'User already sent you spot request, please accept',
}
