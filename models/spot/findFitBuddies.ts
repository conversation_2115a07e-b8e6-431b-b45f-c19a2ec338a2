import { User } from 'models/user';
import { SuccessResponse } from '../common/index';

/**
 * API Path: /spot/fitBuddies-list
 * method: GET
 * query: FindFitBuddiesListQuery
 * response: FindFitBuddiesListResponse
 */

export enum FitBuddiesType {
  FIT_BUDDY = 'FIT_BUDDY',
  FOLLOWER = 'FOLLOWER',
  FOLLOWING = 'FOLLOWING',
  CONNECTED = 'CONNECTED',
}

export enum FitBuddiesStatus {
  ACTIVE = 'ACTIVE',
  BLOCKED = 'BLOCKED',
}

export interface FindFitBuddiesListQuery {
  offset?: number;
  limit?: number;
  status?: FitBuddiesStatus;
  type?: FitBuddiesType;
}

export const enum FindFitBuddiesListErrorMessages {
  FITBUDDIES_NOT_FOUND = 'FitBuddies not found',
}

export interface FindFitBuddiesSuccessResponse extends SuccessResponse {
  data: Partial<User>[];
}

export interface IFitBuddyTypeRes {
  userId: string;
  fitBuddyId: string;
  type: FitBuddiesType;
}

export enum RelationStatusEnum {
  REQUESTED = 'REQUESTED',
  SPOTTED = 'SPOTTED',
  NOT_SPOTTED = 'NOT_SPOTTED',
  SPOT_BACK = 'SPOT_BACK',
}
