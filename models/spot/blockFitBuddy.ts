import { SuccessResponse } from '../common/index';

/**
 * API Path: /spot/block-fitBuddy/{id}
 * method: PATCH
 * response: BlockFitBuddySuccessResponse
 */

export const enum BlockFitBuddyErrorMessages {
  CAN_NOT_BLOCK_FITBUDDY = 'Can not block FitBuddy',
  CAN_NOT_BLOCK_USER = 'Can not block',
  FITBUDDY_NOT_FOUND = 'FitBuddy not found',
  ALREADY_BLOCKED = 'Already blocked',
}

export const enum BlockFitBuddySuccessMessages {
  FITBUDDY_BLOCKED_SUCCESSFULLY = 'Blocked successfully',
}

export interface BlockFitBuddySuccessResponse extends SuccessResponse {
  data: {
    message?: BlockFitBuddySuccessMessages;
  };
}
