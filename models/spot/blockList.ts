import { User } from 'models/user';
import { SuccessResponse } from '../common/index';

/**
 * API Path: /spot/block-list
 * method: GET
 * query: BlockListQuery
 * response: BlockListResponse
 */
export interface BlockListQuery {
  offset?: number;
  limit?: number;
}

export const enum BlockListErrorMessages {
  CAN_NOT_GET_BLOCK_LIST = 'Can not get block list',
}

export interface BlockListResponse extends SuccessResponse {
  data: Partial<User>[];
}
