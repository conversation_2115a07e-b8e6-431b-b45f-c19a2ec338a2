import { SuccessResponse } from '../common/index';

/**
 * API Path: /spot/profile/{userId}
 * method: DELETE
 * response: DeleteSpotProfileSuccessResponse
 */

export const enum DeleteSpotProfileErrorMessages {
  CAN_NOT_DELELE_SPOT_PROFILE = 'Can not delete spot profile',
  SPOT_PROFILE_NOT_FOUND = 'Spot profile not found'
}

export const enum DeleteSpotProfileSuccessMessages {
  SPOT_PROFILE_DELETED_SUCCESSFULLY = 'Spot profile deleted successfully',
}

export interface DeleteSpotProfileSuccessResponse extends SuccessResponse {
  data: {
    message?: DeleteSpotProfileSuccessMessages;
  };
}
